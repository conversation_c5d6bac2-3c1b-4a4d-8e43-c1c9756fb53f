[pytest]
markers =
    asyncio: mark a test as an asyncio coroutine
    django_db: mark a test as requiring database access (provided by pytest-django)
    workflow: mark a test as part of a workflow
    llm: mark a test as related to LLMs
    component: mark a test as a component test
    tool: mark a test as a tool test
    test_type: mark a test with a specific type
    agent: mark a test as an agent test
    agent_test: mark a test as requiring agent test environment mocking
    timeout: mark a test with a specific timeout in seconds (default: 60)

# Enable auto mode for pytest-asyncio
asyncio_mode=auto

# Django database settings
django_db_keepdb=true

# Test paths and file patterns
testpaths = apps
python_files = test_*.py
python_paths = .

# Addopts for concise logs, decently detailed errors, and reports
addopts =
    -q
    --tb=short
    --disable-warnings
    --junitxml=test-results/junit.xml
    --disable-warnings
    --tb=short
    --showlocals
    --durations=10

# Logging configuration for concise but informative logs
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings to ignore light warnings
filterwarnings =
    ignore::pytest.PytestDeprecationWarning
    ignore::django.utils.deprecation.RemovedInDjango60Warning
    ignore::pytest.PytestWarning
