# backend/apps/main/utils/db_connection_utility.py
import logging
import time
import functools
import asyncio
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union, cast
from contextlib import contextmanager

from django.db import connection, connections, transaction, OperationalError, DatabaseError, IntegrityError
from django.db.models import Model, QuerySet
from asgiref.sync import sync_to_async

from apps.main.utils.db_connection_monitor import DBConnectionMonitor

logger = logging.getLogger(__name__)

# Type variables for generic function signatures
T = TypeVar('T')
R = TypeVar('R')

class DatabaseConnectionUtility:
    """
    Utility class for managing database connections with retry logic and connection pooling.

    This class provides methods for safely executing database operations with retry logic
    for transient connection issues, as well as utilities for connection pooling and cleanup.

    Features:
    - Connection pooling through Django's connection pool
    - Retry logic for transient database connection failures
    - Safe transaction management
    - Automatic connection cleanup
    - Integration with DBConnectionMonitor for diagnostics
    """

    # Default configuration
    DEFAULT_MAX_RETRIES = 3
    DEFAULT_RETRY_DELAY = 0.5  # Initial delay in seconds
    DEFAULT_RETRY_BACKOFF = 2.0  # Multiplier for exponential backoff
    DEFAULT_TIMEOUT = 30  # Timeout in seconds

    @staticmethod
    def execute_with_retry(
        sql: str,
        params: Optional[Union[List[Any], Dict[str, Any]]] = None,
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: float = DEFAULT_RETRY_DELAY,
        using: str = 'default'
    ) -> Any:
        """
        Execute a SQL query with retry logic for connection issues.

        Args:
            sql: The SQL query to execute
            params: Parameters for the SQL query
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries (will increase with backoff)
            using: Database connection alias to use

        Returns:
            The result of the query execution

        Raises:
            OperationalError: If all retry attempts fail
        """
        conn = connections[using]
        current_delay = retry_delay

        for attempt in range(max_retries):
            try:
                with conn.cursor() as cursor:
                    result = cursor.execute(sql, params)
                    return result
            except (OperationalError, DatabaseError) as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"Database operation failed (attempt {attempt + 1}/{max_retries}): {e}. "
                        f"Retrying in {current_delay:.2f} seconds..."
                    )
                    time.sleep(current_delay)
                    current_delay *= DatabaseConnectionUtility.DEFAULT_RETRY_BACKOFF
                    # Add some randomness to avoid thundering herd
                    current_delay += current_delay * (0.1 * (2 * (0.5 - time.time() % 1)))
                else:
                    logger.error(f"Database operation failed after {max_retries} attempts: {e}")
                    raise

    @staticmethod
    @contextmanager
    def safe_connection(using: str = 'default'):
        """
        Context manager for safely using a database connection with automatic cleanup.

        Args:
            using: Database connection alias to use

        Yields:
            A database connection from the connection pool
        """
        conn = connections[using]
        # Ensure the connection is established before yielding
        if conn.connection is None or conn.connection.closed:
            conn.ensure_connection()
        try:
            yield conn
        finally:
            # Ensure the connection is returned to the pool
            # but don't close it completely as it's managed by Django
            if conn.connection is not None and not conn.connection.closed:
                # Reset the connection state but keep it in the pool
                conn.close()

    @staticmethod
    @contextmanager
    def safe_cursor(using: str = 'default'):
        """
        Context manager for safely using a database cursor with automatic cleanup.

        Args:
            using: Database connection alias to use

        Yields:
            A database cursor
        """
        with DatabaseConnectionUtility.safe_connection(using) as conn:
            with conn.cursor() as cursor:
                try:
                    yield cursor
                finally:
                    # The cursor is automatically closed by the context manager
                    pass

    @staticmethod
    def with_retry(
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: float = DEFAULT_RETRY_DELAY,
        retryable_exceptions: tuple = (OperationalError, DatabaseError)
    ) -> Callable[[Callable[..., T]], Callable[..., T]]:
        """
        Decorator for adding retry logic to database operations.

        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries (will increase with backoff)
            retryable_exceptions: Tuple of exception types to retry on

        Returns:
            Decorated function with retry logic
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> T:
                current_delay = retry_delay
                func_name = getattr(func, "__name__", str(func))

                for attempt in range(max_retries):
                    try:
                        return func(*args, **kwargs)
                    except retryable_exceptions as e:
                        if attempt < max_retries - 1:
                            logger.warning(
                                f"{func_name} failed (attempt {attempt + 1}/{max_retries}): {e}. "
                                f"Retrying in {current_delay:.2f} seconds..."
                            )
                            time.sleep(current_delay)
                            current_delay *= DatabaseConnectionUtility.DEFAULT_RETRY_BACKOFF
                            # Add some randomness to avoid thundering herd
                            current_delay += current_delay * (0.1 * (2 * (0.5 - time.time() % 1)))
                        else:
                            logger.error(f"{func_name} failed after {max_retries} attempts: {e}")
                            raise
            return wrapper
        return decorator

    @staticmethod
    def async_with_retry(
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: float = DEFAULT_RETRY_DELAY,
        retryable_exceptions: tuple = (OperationalError, DatabaseError)
    ) -> Callable[[Callable[..., T]], Callable[..., T]]:
        """
        Decorator for adding retry logic to async database operations.

        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries (will increase with backoff)
            retryable_exceptions: Tuple of exception types to retry on

        Returns:
            Decorated async function with retry logic
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> T:
                current_delay = retry_delay
                func_name = getattr(func, "__name__", str(func))

                for attempt in range(max_retries):
                    try:
                        return await func(*args, **kwargs)
                    except retryable_exceptions as e:
                        if attempt < max_retries - 1:
                            logger.warning(
                                f"{func_name} failed (attempt {attempt + 1}/{max_retries}): {e}. "
                                f"Retrying in {current_delay:.2f} seconds..."
                            )
                            await asyncio.sleep(current_delay)
                            current_delay *= DatabaseConnectionUtility.DEFAULT_RETRY_BACKOFF
                            # Add some randomness to avoid thundering herd
                            current_delay += current_delay * (0.1 * (2 * (0.5 - time.time() % 1)))
                        else:
                            logger.error(f"{func_name} failed after {max_retries} attempts: {e}")
                            raise
            return wrapper
        return decorator

    @staticmethod
    def safe_transaction(using: str = 'default', savepoint: bool = True):
        """
        Decorator for safely executing a function within a database transaction.

        Args:
            using: Database connection alias to use
            savepoint: Whether to use savepoints

        Returns:
            Decorated function with transaction management
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> T:
                with transaction.atomic(using=using, savepoint=savepoint):
                    return func(*args, **kwargs)
            return wrapper
        return decorator

    @staticmethod
    def async_safe_transaction(using: str = 'default', savepoint: bool = True):
        """
        Decorator for safely executing an async function within a database transaction.

        Args:
            using: Database connection alias to use
            savepoint: Whether to use savepoints

        Returns:
            Decorated async function with transaction management
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> T:
                # Use sync_to_async to run the function in a transaction
                @sync_to_async
                def run_in_transaction():
                    with transaction.atomic(using=using, savepoint=savepoint):
                        # Since we're in a sync context now, we need to call the original async function
                        # in a way that it can be awaited in the async context but executed in the sync context
                        # Instead of getting the event loop, which might not exist in the thread,
                        # we'll use a simpler approach that just returns the result directly
                        # This is a workaround for the "no current event loop in thread" error
                        return func(*args, **kwargs)

                # The result of run_in_transaction is the coroutine object, not the result
                # We need to await it to get the actual result
                coro = await run_in_transaction()
                return await coro
            return wrapper
        return decorator

    @staticmethod
    def get_or_create_with_retry(
        model_class: type,
        defaults: Optional[Dict[str, Any]] = None,
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: float = DEFAULT_RETRY_DELAY,
        **kwargs: Any
    ) -> tuple:
        """
        Wrapper around Django's get_or_create with retry logic.

        Args:
            model_class: The Django model class
            defaults: Default values for creating a new object
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries
            **kwargs: Lookup parameters

        Returns:
            Tuple of (object, created) as per Django's get_or_create
        """
        current_delay = retry_delay

        for attempt in range(max_retries):
            try:
                with transaction.atomic():
                    return model_class.objects.get_or_create(defaults=defaults or {}, **kwargs)
            except IntegrityError:
                # Handle race condition: another process created the object
                # Try to get the existing object
                try:
                    obj = model_class.objects.get(**kwargs)
                    return obj, False
                except model_class.DoesNotExist:
                    # Object was deleted between creation attempt and get, retry
                    if attempt < max_retries - 1:
                        logger.warning(
                            f"get_or_create race condition (attempt {attempt + 1}/{max_retries}): "
                            f"Object was created and deleted. Retrying in {current_delay:.2f} seconds..."
                        )
                        time.sleep(current_delay)
                        current_delay *= DatabaseConnectionUtility.DEFAULT_RETRY_BACKOFF
                        continue
                    else:
                        logger.error(f"get_or_create failed after {max_retries} attempts due to race conditions")
                        raise
            except (OperationalError, DatabaseError) as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"get_or_create failed (attempt {attempt + 1}/{max_retries}): {e}. "
                        f"Retrying in {current_delay:.2f} seconds..."
                    )
                    time.sleep(current_delay)
                    current_delay *= DatabaseConnectionUtility.DEFAULT_RETRY_BACKOFF
                else:
                    logger.error(f"get_or_create failed after {max_retries} attempts: {e}")
                    raise

        # This should never be reached, but just in case
        raise RuntimeError(f"get_or_create_with_retry completed without returning a value")

    @staticmethod
    async def async_get_or_create_with_retry(
        model_class: type,
        defaults: Optional[Dict[str, Any]] = None,
        max_retries: int = DEFAULT_MAX_RETRIES,
        retry_delay: float = DEFAULT_RETRY_DELAY,
        **kwargs: Any
    ) -> tuple:
        """
        Async wrapper around Django's get_or_create with retry logic.

        Args:
            model_class: The Django model class
            defaults: Default values for creating a new object
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries
            **kwargs: Lookup parameters

        Returns:
            Tuple of (object, created) as per Django's get_or_create
        """
        @sync_to_async
        def get_or_create_sync():
            return DatabaseConnectionUtility.get_or_create_with_retry(
                model_class, defaults, max_retries, retry_delay, **kwargs
            )

        return await get_or_create_sync()

    @staticmethod
    def add_connection_monitoring(db_connection_monitor=None):
        """
        Decorator for adding database connection monitoring to a function.

        Args:
            db_connection_monitor: Optional db_connection_monitor fixture from pytest

        Returns:
            Decorated function with connection monitoring
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> T:
                # If no monitor is provided, create a temporary one using DBConnectionMonitor
                if db_connection_monitor is None:
                    initial_snapshot = DBConnectionMonitor.get_connection_snapshot()
                    try:
                        return func(*args, **kwargs)
                    finally:
                        final_snapshot = DBConnectionMonitor.get_connection_snapshot()
                        DBConnectionMonitor.log_connection_statistics(initial_snapshot, final_snapshot)
                        DBConnectionMonitor.cleanup_leaked_connections(initial_snapshot, final_snapshot)
                else:
                    # Use the provided monitor
                    initial_snapshot = db_connection_monitor["get_connection_snapshot"]()
                    try:
                        return func(*args, **kwargs)
                    finally:
                        final_snapshot = db_connection_monitor["get_connection_snapshot"]()
                        db_connection_monitor["cleanup_leaked_connections"](initial_snapshot, final_snapshot)
            return wrapper
        return decorator

    @staticmethod
    def async_add_connection_monitoring(db_connection_monitor=None):
        """
        Decorator for adding database connection monitoring to an async function.

        Args:
            db_connection_monitor: Optional db_connection_monitor fixture from pytest

        Returns:
            Decorated async function with connection monitoring
        """
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> T:
                # If no monitor is provided, create a temporary one using DBConnectionMonitor
                if db_connection_monitor is None:
                    initial_snapshot = DBConnectionMonitor.get_connection_snapshot()
                    try:
                        return await func(*args, **kwargs)
                    finally:
                        final_snapshot = DBConnectionMonitor.get_connection_snapshot()
                        DBConnectionMonitor.log_connection_statistics(initial_snapshot, final_snapshot)
                        DBConnectionMonitor.cleanup_leaked_connections(initial_snapshot, final_snapshot)
                else:
                    # Use the provided monitor
                    initial_snapshot = db_connection_monitor["get_connection_snapshot"]()
                    try:
                        return await func(*args, **kwargs)
                    finally:
                        final_snapshot = db_connection_monitor["get_connection_snapshot"]()
                        db_connection_monitor["cleanup_leaked_connections"](initial_snapshot, final_snapshot)
            return wrapper
        return decorator

    @staticmethod
    def limit_concurrent_db_operations(max_concurrent: int = 5):
        """
        Decorator for limiting the number of concurrent database operations in async functions.

        Args:
            max_concurrent: Maximum number of concurrent operations

        Returns:
            Decorated async function with concurrency limiting
        """
        # Create a semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)

        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> T:
                async with semaphore:
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
