# Goali Agents and Workflows: Comprehensive Guide

*Last Updated: 2025-01-31*

## Recent Updates - Workflow Benchmarking System

### June 2025: MentorService Singleton Implementation ✅

**MAJOR ARCHITECTURAL UPDATE**: Implemented MentorService as a singleton service that maintains the Mentor's state and handles all user-facing communication. This creates a central hub for <PERSON><PERSON> interactions, ensuring consistency across workflow boundaries while maintaining the Mentor's system-level responsibilities.

#### Key Features Implemented

1. **MentorService Singleton**: Per-user singleton pattern ensuring consistent Mentor state across all interactions
2. **ConversationDispatcher Integration**: All messages now route through MentorService before and after workflow execution
3. **Workflow Result Processing**: All workflow outputs pass through MentorService for consistent user-facing communication
4. **State Management**: Persistent Mentor state including trust levels, communication preferences, and conversation context
5. **Memory Management**: Built-in memory system for storing conversation-related information
6. **Workflow Boundaries**: <PERSON><PERSON> can participate in workflows while maintaining system-level responsibilities

### June 2025: Wheel Generation Graph Enhancement ✅

**ARCHITECTURAL ENHANCEMENT**: Re-evaluated and enhanced the wheel generation graph to fully respect the new MentorService singleton architecture, ensuring optimal integration and improved user experience.

#### Key Enhancements Implemented

1. **Enhanced Metadata Collection**: Added comprehensive metadata extraction for Mentor processing including:
   - User insights (trust level, psychological profile, mood, energy level)
   - Wheel characteristics (activity count, domains covered, personalization level)
   - Communication guidance (style, tone, detail level, encouragement level)
   - Safety context (approval status, safety level, trust phase appropriateness)

2. **Improved Workflow Evaluation**: Enhanced async workflow manager to better handle wheel packages with:
   - Priority extraction of Mentor-crafted user messages
   - Enhanced wheel summaries with comprehensive activity descriptions
   - Workflow insights integration for better semantic evaluation

3. **Updated Message Specifications**: Enhanced `MESSAGE_SPECIFICATIONS.md` to include:
   - `mentor_context` with workflow summary, user insights, and communication guidance
   - `workflow_insights` with execution summary and personalization quality
   - Enhanced data structure for better UI integration

4. **Comprehensive Data Flow Documentation**: Updated `data_flow.md` to reflect:
   - MentorService pre/post-processing integration
   - Enhanced metadata collection throughout workflow execution
   - Improved user experience through personalized communication

5. **Verified Real-Mode Execution**: Confirmed system works properly with:
   - Real LLM/tools/database execution (semantic score: 0.85)
   - Accurate token counting (1.1k input + 700 output = 1.8k total)
   - Proper agent communications tracking with enhanced metadata
   - Complete workflow execution without silent fallbacks

### January 2025: Real Mode Implementation Completed ✅

**MAJOR UPDATE**: The orchestrator agent and all workflow agents now support real mode execution with proper LLM, tool, and database integration. This resolves the "Real mode execution not yet implemented" error and enables true workflow benchmarking.

#### Real Mode Implementation Features

1.  **Complete Agent Support**: All agents (Orchestrator, Resource, Engagement, Psychological, Strategy, Activity, Ethical) now support real mode execution
2.  **Automatic LLM Configuration**: Intelligent selection of LLM configs with fallback mechanisms
3.  **Execution Mode Flexibility**: Support for full real mode, full mock mode, and hybrid configurations
4.  **Centralized Configuration**: Helper function for consistent agent configuration across execution modes
5.  **Enhanced Logging**: Detailed execution mode tracking and configuration logging

### June 2025: Benchmarking System Fixes ✅

**CRITICAL FIXES**: Resolved test failures and verified proper workflow vs agent evaluation display in admin interface.

#### Key Fixes Applied

1.  **MockWorkflow Parameter Compatibility**: Fixed missing `user_profile_id` parameter in MockWorkflow class method signatures
    *   Updated `backend/apps/main/testing/mock_workflow.py`
    *   Fixed test compatibility in `test_workflow_semantic_evaluation.py`
    *   Corrected lambda functions in integration tests

2.  **User Profile ID Integration**: Ensured proper propagation of user profile context through benchmarking system
    *   All workflow benchmarking tests now pass
    *   Proper parameter compatibility across inheritance hierarchy

3.  **Workflow vs Agent Evaluation Display**: Verified admin interface correctly distinguishes between evaluation types
    *   Separate modal templates for workflow and agent evaluations
    *   Proper UI differentiation with distinct styling and content focus
    *   Workflow modals focus on agent coordination and system performance
    *   Agent modals focus on LLM configuration and individual performance metrics

### January 2025: Enhanced Workflow Benchmarking

The workflow benchmarking system has been significantly enhanced to support proper routing and execution of workflow benchmarks:

#### Key Improvements

1.  **Automatic Routing**: Scenarios with `workflow_type` metadata are now automatically routed to workflow benchmarks instead of agent benchmarks.

2.  **Mock Workflow Execution**: Workflow benchmarks use mock implementations that simulate real workflow behavior without requiring LLM clients or database connections.

3.  **Context-Aware Generation**: Mock workflows generate contextually appropriate outputs based on scenario metadata (trust levels, user preferences, etc.).

4.  **Agent Communications Tracking**: Comprehensive monitoring of agent interactions, state transitions, and execution metrics during workflow benchmarks.

5.  **Complete Integration**: Workflow benchmarks integrate seamlessly with the existing benchmarking infrastructure, producing standard BenchmarkRun results.

#### Implementation Details

*   **Routing Logic**: The `BenchmarkManager.run_benchmark()` method checks for `workflow_type` in scenario metadata and routes accordingly.
*   **Mock Workflows**: Each workflow type has a corresponding mock implementation (e.g., `WheelWorkflowBenchmarkManager` for wheel generation).
*   **Tool Mocking**: MockToolRegistry provides realistic tool responses without external dependencies.
*   **Agent Communications Tracking**: `AgentCommunicationTracker` service monitors all agent interactions and state transitions.
*   **Result Structure**: Workflow benchmarks produce the same result structure as agent benchmarks for consistency.

#### Agent Communications Tracking

The system now provides comprehensive visibility into agent interactions during workflow execution:

**Data Captured**:
*   **Agent Executions**: Complete input/output data for each agent in the workflow
*   **State Transitions**: Before/after workflow state for each agent execution
*   **Performance Metrics**: Execution timing, success rates, and error information
*   **Summary Statistics**: Total communications, success rates, duration metrics

**Admin Interface Features**:
*   **Interactive Timeline**: Visual representation of agent execution flow
*   **Detailed Analysis**: Expandable views for input/output data inspection
*   **Summary Dashboard**: Key metrics and statistics at a glance
*   **Raw Data Access**: Complete JSON data for debugging and analysis

**Technical Components**:
*   **AgentCommunicationTracker**: Core service for tracking agent interactions
*   **BenchmarkRun.agent_communications**: JSONField storing complete communication data
*   **Enhanced Admin Templates**: Rich UI components for data visualization

#### Example Usage

```python
# Scenario with workflow_type automatically routes to workflow benchmark
scenario_metadata = {
    'workflow_type': 'wheel_generation',
    'user_profile_context': {
        'trust_phase': 'Foundation',
        'trust_level': 35
    }
}

# This will automatically use WheelWorkflowBenchmarkManager
result = await benchmark_manager.run_benchmark(scenario_id, params)
```

## Table of Contents

1.  [Overview](#overview)
2.  [Core Architecture](#core-architecture)
3.  [Agents](#agents)
4.  [Workflows](#workflows)
5.  [Technical Implementation](#technical-implementation)
6.  [Data Flow](#data-flow)
7.  [Integration Patterns](#integration-patterns)

---

## Overview

Goali is built on two fundamental architectural elements that work together to create personalized, adaptive user experiences:

*   **Agents**: Specialized AI components that handle specific aspects of user interaction and decision-making
*   **Workflows**: Orchestrated sequences of agent interactions that accomplish complex user goals

This multi-agent system leverages LangGraph for workflow orchestration and provides a scalable, modular approach to AI-driven personal development.

### Key Principles

*   **Specialization**: Each agent has a focused responsibility and expertise area
*   **Orchestration**: Workflows coordinate agents to achieve complex objectives
*   **Personalization**: All interactions are tailored to individual user profiles
*   **Ethical Oversight**: Built-in validation ensures user well-being and safety
*   **Adaptability**: System learns and adapts based on user feedback and patterns

---

## Core Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ ConvDispatcher  │ →  │ Agent Workflows │ →  │ WorkflowResultHandler │
│ (Entry Point)   │    │ (LangGraph)     │    │ (Output)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Message         │    │ Agent           │    │ WebSocket       │
│ Classification  │    │ Coordination    │    │ Delivery        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Agent Ecosystem

The system employs 9 specialized agents working within LangGraph workflows:

1.  **ConversationDispatcher** - Entry point and workflow routing
2.  **Orchestrator Agent** - Workflow coordination and state management
3.  **Mentor Agent** - User-facing conversational interface
4.  **Resource & Capacity Agent** - Environmental and resource analysis
5.  **Engagement & Pattern Analytics Agent** - Historical pattern analysis
6.  **Psychological Monitoring Agent** - Psychological state assessment
7.  **Strategy Agent** - Decision synthesis and planning
8.  **Wheel/Activity Agent** - Activity generation and customization
9.  **Ethical Oversight Agent** - Safety and ethical validation

---

## Agents

### 1. ConversationDispatcher

**Role**: System entry point that processes user messages and routes to appropriate workflows.

**Key Responsibilities**:
*   Message classification and intent detection
*   Context extraction from user input
*   Workflow selection and initiation
*   WebSocket session management

**Data Access**:
*   Read: UserProfile (completion status)
*   Write: HistoryEvent (workflow initiation)
*   Execute: Classification and routing tools

**Processing Logic**:
1.  Extract context (mood, environment, time availability)
2.  Classify message intent (wheel_generation, activity_feedback, discussion)
3.  Create standardized context packet
4.  Launch appropriate workflow via Celery

### 2. Orchestrator Agent

**Role**: Coordinates workflow execution and manages state transitions between agents.

**Key Responsibilities**:
*   Agent sequence determination
*   Context distribution to specialized agents
*   Conflict resolution using priority framework
*   Error handling and recovery
*   Workflow state tracking

**Data Access**:
*   Read: All data models (coordination purposes)
*   Write: HistoryEvent, AgentActivation records
*   Memory: None (stateless for MVP)

**Processing Logic**:
1.  Parse context to identify task requirements
2.  Determine optimal agent sequence
3.  Extract relevant context subsets for each agent
4.  Integrate specialized agent outputs
5.  Apply priority hierarchy for conflict resolution

### 3. Mentor Agent & MentorService

**Role**: Primary user-facing conversational interface that synthesizes agent outputs into natural dialogue, now enhanced with singleton service architecture.

**Key Responsibilities**:
*   Natural language conversation management
*   Trust-based communication adaptation
*   Activity presentation and explanation
*   Feedback collection and processing
*   Philosophical framing maintenance
*   Cross-workflow state persistence
*   System-level user communication coordination

**MentorService Architecture**:
*   **Singleton Pattern**: One instance per user profile, ensuring state consistency
*   **State Management**: Persistent conversation context, trust levels, and preferences
*   **Message Routing**: All user messages processed before workflow execution
*   **Result Processing**: All workflow outputs formatted for user delivery
*   **Memory System**: Built-in caching for conversation-related information
*   **Workflow Integration**: Participates in workflows while maintaining system responsibilities

**Data Access**:
*   Read: UserProfile, HistoryEvent, UserFeedback, TrustLevel
*   Write: HistoryEvent, UserFeedback, CurrentMood
*   Memory: Communication preferences, conversation context, workflow history

**Processing Logic**:
1.  Process incoming messages through MentorService singleton
2.  Enhance messages with Mentor context and assessments
3.  Participate in workflow execution as needed
4.  Process workflow results for user-friendly delivery
5.  Maintain persistent state across workflow boundaries
6.  Generate transition requests when needed

### 4. Resource & Capacity Agent

**Role**: Analyzes user's available resources, environment, and constraints.

**Key Responsibilities**:
*   Environmental context analysis
*   Resource inventory assessment
*   Time availability evaluation
*   Physical and cognitive capability assessment
*   Constraint identification

**Data Access**:
*   Read: UserEnvironment, Inventory, UserResource, Skill, UserLimitation
*   Write: Inventory (direct updates)
*   Recommend: Capability and UserLimitation updates

**Output**: Resource context document with environmental ratings, resource availability, time parameters, and capability metrics.

### 5. Engagement & Pattern Analytics Agent

**Role**: Analyzes historical interaction patterns and user preferences.

**Key Responsibilities**:
*   Historical pattern analysis
*   Domain preference extraction
*   Completion vs. abandonment pattern detection
*   Temporal pattern identification
*   Behavioral insight generation

**Data Access**:
*   Read: HistoryEvent, UserFeedback, Preference, ActivityTailored
*   Write: None (analysis only)
*   Memory: Domain engagement metrics, pattern confidence scores

**Output**: Engagement profile with domain preferences, temporal patterns, and behavioral insights.

### 6. Psychological Monitoring Agent

**Role**: Assesses psychological state and determines appropriate challenge levels.

**Key Responsibilities**:
*   Current psychological state assessment
*   Trust phase determination (Foundation/Expansion)
*   HEXACO trait analysis
*   Growth opportunity identification
*   Challenge calibration recommendations

**Data Access**:
*   Read: UserTraitInclination, Belief, TrustLevel, UserGoal, Inspiration
*   Write: CurrentMood
*   Memory: Trait expression patterns, trust development history

**Output**: Psychological assessment with mood evaluation, trust phase, trait analysis, and challenge parameters.

### 7. Strategy Agent

**Role**: Synthesizes multi-agent inputs into comprehensive activity strategy.

**Key Responsibilities**:
*   Multi-agent input integration
*   Gap analysis between traits and requirements
*   Domain distribution planning
*   Challenge calibration strategy
*   Growth pathway definition

**Data Access**:
*   Read: UserGoal, UserTraitInclination, TrustLevel, Preference, Belief
*   Write: None (strategy formulation only)
*   Memory: Baseline strategy parameters, trust-based adaptations

**Output**: Strategy framework with challenge calibration, domain distribution, and activity selection criteria.

### 8. Wheel/Activity Agent

**Role**: Generates concrete activities and constructs the activity wheel.

**Key Responsibilities**:
*   Activity selection from GenericActivity catalog
*   Activity customization and tailoring
*   Wheel construction with probability weights
*   Value proposition development
*   Resource requirement matching

**Data Access**:
*   Read: GenericActivity, UserProfile, UserEnvironment, Inventory
*   Write: Wheel, WheelItem, ActivityTailored
*   Memory: None (stateless)

**Output**: Complete wheel package with 6-8 activities, probability weights, and customized instructions.

### 9. Ethical Oversight Agent

**Role**: Validates all activities and decisions against ethical principles.

**Key Responsibilities**:
*   Activity ethical alignment review
*   Psychological safety validation
*   Challenge level appropriateness verification
*   Transparency and autonomy confirmation
*   Safety boundary enforcement

**Data Access**:
*   Read: Wheel, WheelItem, ActivityTailored, UserTraitInclination, Belief, TrustLevel
*   Write: None (validation only)
*   Memory: None (stateless)

**Output**: Ethical validation report with approval status and modification recommendations.

---

## Workflows

### Primary Workflow Types

| Workflow            | Purpose                                          | Duration | Key Agents                       |
| ------------------- | ------------------------------------------------ | -------- | -------------------------------- |
| **wheel_generation**  | Create personalized activity suggestions       | ~15s     | All agents in sequence           |
| **discussion**        | Handle general conversation and clarification    | Variable | Mentor, Orchestrator             |
| **onboarding**        | Process new user questionnaire into profile      | ~20s     | All agents for profile creation  |
| **activity_feedback** | Process activity completion feedback             | ~10s     | Mentor, Engagement Analytics     |
| **post_spin**         | Handle activity selection from wheel             | ~8s      | Mentor, Orchestrator             |
| **pre_spin_feedback** | Collect context before wheel generation        | ~5s      | Mentor, Orchestrator             |

### Workflow Architecture Patterns

#### 1. Sequential Processing Pattern
Used in **wheel_generation** and **onboarding**:
```
Orchestrator → Resource → Engagement → Psychological → Strategy → Activity → Ethical → Orchestrator
```

#### 2. Conversational Loop Pattern
Used in **discussion** workflow:
```
Orchestrator → Mentor ⟲ (until goal met) → Orchestrator → Termination
```

#### 3. Feedback Processing Pattern
Used in **activity_feedback**:
```
Orchestrator → Engagement Analytics → Psychological → Mentor → Orchestrator
```

### Workflow State Management

Each workflow maintains state through LangGraph's StateGraph with standardized state models:

```python
class WorkflowState(BaseModel):
    workflow_id: str
    user_profile_id: str
    user_ws_session_name: Optional[str]
    context_packet: Dict[str, Any]
    
    # Agent outputs
    resource_context: Optional[Dict[str, Any]]
    engagement_analysis: Optional[Dict[str, Any]]
    psychological_assessment: Optional[Dict[str, Any]]
    strategy_framework: Optional[Dict[str, Any]]
    wheel: Optional[Dict[str, Any]]
    ethical_validation: Optional[Dict[str, Any]]
    
    # State tracking
    current_stage: WorkflowStage
    next_agent: Optional[str]
    completed: bool
    error: Optional[str]
```

---

## Technical Implementation

### LangGraph Integration

*   **StateGraph**: Defines workflow structure and agent transitions
*   **Conditional Edges**: Route execution based on state conditions
*   **Agent Nodes**: Encapsulate individual agent logic
*   **Memory Management**: Persistent and transient memory systems

### Agent Base Class Pattern

```python
class LangGraphAgent:
    async def process(self, state: WorkflowState) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        # Extract state information
        # Process based on agent's responsibilities  
        # Return output_data and state_updates
        return output_data, state_updates
```

### Asynchronous Processing

*   **Celery Tasks**: Handle long-running workflow execution
*   **WebSocket Integration**: Real-time communication with frontend
*   **Result Handling**: Automatic delivery of workflow results
*   **Error Recovery**: Graceful handling of failures and timeouts

---

## Data Flow

### Message Processing Flow

1.  **User Input** → WebSocket → UserSessionConsumer
2.  **Message Echo** → Immediate user feedback
3.  **MentorService Pre-Processing** → Message enhancement with Mentor context
4.  **ConversationDispatcher** → Context extraction and classification from enhanced message
5.  **Workflow Initiation** → Celery task with context packet including Mentor state
6.  **Agent Processing** → Sequential or parallel agent execution (Mentor can participate)
7.  **Result Handling** → WorkflowResultHandler processes outputs through MentorService
8.  **MentorService Post-Processing** → Results formatted for user-friendly delivery
9.  **Response Delivery** → WebSocket delivery to client with Mentor-enhanced responses

### Context Packet Structure

```json
{
  "user_id": "user-profile-uuid",
  "session_timestamp": "2023-10-15T14:30:00Z",
  "reported_mood": "focused",
  "reported_environment": "home", 
  "reported_time_availability": "30 minutes",
  "reported_focus": "creative activities",
  "extraction_confidence": 0.85,
  "user_ws_session_name": "client_session_uuid"
}
```

### Memory Systems

#### Agent Memory
*   **Psychological Monitoring**: Trait patterns, trust development
*   **Engagement Analytics**: Domain metrics, completion patterns  
*   **Strategy Agent**: Baseline parameters, adaptation patterns
*   **Mentor Agent**: Communication preferences, effective approaches

#### Workflow Memory
*   **State Persistence**: LangGraph state management
*   **Context Continuity**: Cross-agent data sharing
*   **Error Context**: Recovery information and retry parameters

---

## Integration Patterns

### Error Handling Strategy

1.  **Agent-Level Errors**: Graceful degradation with fallback responses
2.  **Workflow-Level Errors**: Error Handler Agent provides recovery
3.  **System-Level Errors**: Notification and retry mechanisms
4.  **User Communication**: Transparent error communication without technical details

### Trust-Based Adaptation

*   **Foundation Phase** (0-60%): High success probability, clear structure
*   **Expansion Phase** (61-100%): Moderate challenge, growth focus
*   **Communication Adaptation**: Tone and complexity based on trust level
*   **Challenge Calibration**: Dynamic adjustment based on user response

### Ethical Framework Integration

*   **Benevolence**: All decisions prioritize user well-being
*   **Fairness**: Balanced representation without bias
*   **Transparency**: Clear explanations and rationales
*   **Autonomy**: Respect for user choice and boundaries
*   **Safety**: Psychological and physical safety validation

### Quality Assurance

*   **Output Benchmarks**: Standardized quality criteria for each agent
*   **Confidence Scoring**: Reliability metrics for all assessments
*   **Validation Chains**: Multi-layer validation before user delivery
*   **Continuous Learning**: Pattern recognition and system improvement

---

## Benchmarking System Architecture

### Agent vs. Workflow Benchmarking

The Goali benchmarking system supports two distinct evaluation approaches, each designed for different optimization goals:

#### Agent Benchmarking (`AgentBenchmarker`)

**Purpose**: Evaluate individual agent performance in isolation to optimize LLM configurations and agent instructions.

**Key Characteristics**:
*   **Single Agent Focus**: Tests one agent at a time with controlled inputs
*   **LLM Configuration Testing**: Evaluates different models, temperatures, and prompt strategies
*   **Instruction Optimization**: Measures how well agent instructions guide LLM behavior
*   **Semantic Quality Assessment**: Uses evaluator LLMs to assess output quality against criteria
*   **Context Variable Testing**: Tests agent performance across different user contexts (trust levels, moods, environments)

**Data Captured**:
*   **LLM Metrics**: Model used, temperature, token usage, cost estimation
*   **Performance Metrics**: Execution time, success rate, error patterns
*   **Semantic Evaluation**: Quality scores across multiple dimensions (relevance, accuracy, helpfulness)
*   **Context Sensitivity**: Performance variation across different user contexts
*   **Instruction Effectiveness**: How well the agent follows its defined instructions

**Optimization Focus**:
*   Fine-tuning LLM model selection and parameters
*   Improving agent instruction clarity and effectiveness
*   Optimizing prompt engineering strategies
*   Balancing cost vs. quality trade-offs

#### Workflow Benchmarking (`WorkflowBenchmarker`)

**Purpose**: Evaluate complete multi-agent workflows to optimize coordination, state management, and system-level performance.

**Key Characteristics**:
*   **Multi-Agent Coordination**: Tests agent interactions and handoffs within workflows
*   **State Management**: Evaluates how well workflow state is maintained and passed between agents
*   **System Performance**: Measures end-to-end execution time and resource usage
*   **Agent Communication Tracking**: Monitors all agent interactions and data flow
*   **Workflow Logic Validation**: Ensures proper execution flow and error handling

**Data Captured**:
*   **Agent Communications**: Complete input/output data for each agent in the workflow
*   **State Transitions**: Before/after workflow state for each agent execution
*   **Coordination Metrics**: Handoff efficiency, state consistency, error propagation
*   **System Performance**: Total execution time, stage-by-stage timing, resource utilization
*   **Workflow Integrity**: Proper execution flow, error handling, recovery mechanisms

**Optimization Focus**:
*   Improving agent coordination and handoff efficiency
*   Optimizing workflow state management
*   Reducing system-level bottlenecks
*   Enhancing error handling and recovery
*   Balancing workflow complexity vs. performance

### Technical Implementation Differences

#### Agent Benchmarking Implementation

**Location**: `backend/apps/main/services/benchmark_manager.py`

**Core Components**:
```python
class AgentBenchmarker:
    async def run_agent_benchmark(self, scenario, agent_role, params):
        # Single agent execution with controlled inputs
        # LLM configuration testing
        # Semantic evaluation with multiple evaluator models
        # Context variable testing across ranges
```

**Key Features**:
*   **Scenario-Based Testing**: Uses `BenchmarkScenario` objects with specific agent roles
*   **Context Variable Ranges**: Tests across trust levels, moods, and environments
*   **Semantic Evaluation**: Multi-dimensional quality assessment
*   **LLM Configuration Matrix**: Tests different models and parameters
*   **Statistical Analysis**: Confidence intervals, significance testing

#### Workflow Benchmarking Implementation

**Location**: `backend/apps/main/services/async_workflow_manager.py`

**Core Components**:
```python
class WorkflowBenchmarker:
    async def run_workflow_benchmark(self, scenario, workflow_type, params):
        # Multi-agent workflow execution
        # Agent communication tracking
        # State management validation
        # System performance measurement
```

**Key Features**:
*   **LangGraph Integration**: Uses actual workflow definitions for testing
*   **Agent Communication Tracking**: Monitors all agent interactions
*   **Mock Tool Registry**: Provides realistic tool responses without external dependencies
*   **Stage Performance Analysis**: Detailed timing for each workflow stage
*   **Token Usage Tracking**: Comprehensive token usage across all agents

### Benchmarking Data Structures

#### Agent Benchmark Results
```python
{
    "agent_role": "mentor",
    "llm_model": "gpt-4o-mini",
    "llm_temperature": 0.7,
    "semantic_score": 0.85,
    "semantic_evaluation_details": {
        "relevance": 0.9,
        "accuracy": 0.8,
        "helpfulness": 0.85
    },
    "context_sensitivity": {
        "trust_level_variance": 0.12,
        "mood_impact": 0.08
    },
    "performance_metrics": {
        "mean_duration_ms": 1250,
        "success_rate": 0.95,
        "token_usage": "1200/800"
    }
}
```

#### Workflow Benchmark Results
```python
{
    "workflow_type": "wheel_generation",
    "agent_communications": {
        "enabled": true,
        "agents": [
            {
                "agent": "orchestrator",
                "stage": "initialization",
                "input": {...},
                "output": {...},
                "duration_ms": 150,
                "success": true
            }
        ],
        "summary": {
            "total_communications": 8,
            "success_rate": 1.0,
            "total_duration_ms": 12500
        }
    },
    "stage_performance": {
        "orchestrator_init": 150,
        "resource_analysis": 2100,
        "strategy_synthesis": 3200
    },
    "system_metrics": {
        "total_execution_time": 12.5,
        "total_token_usage": 15000,
        "agent_coordination_efficiency": 0.92
    }
}
```

#### Execution Mode Tracking in Results
Workflow results are enriched with metadata to indicate the actual execution mode used:
```python
# Example structure in workflow_result from backend/apps/main/graphs/wheel_generation_graph.py
workflow_result = {
    # ... other result data ...
    "execution_mode": "real" if (any_real_llm or any_real_db or any_real_tools) else "mock", # Based on actual usage
    "real_llm_used": any_real_llm_actually_used,
    "real_tools_used": any_real_tools_actually_used,
    "real_db_used": any_real_db_actually_used,
    "tool_usage": extracted_tool_usage_counts, # From _extract_tool_usage
    "agent_communications": extracted_agent_communications_data # From _extract_agent_communications
    # Potentially: requested_execution_mode vs actual_execution_modes for detailed comparison
}
```
If a workflow runs entirely in mock mode, the `execution_mode` will be "mock", and specific metadata like `"mock_execution_note"` might be included.
Helper functions like `_extract_tool_usage(result: WheelGenerationState, ...)` and `_extract_agent_communications(result: WheelGenerationState)` (both within [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:)) are used to gather relevant data from the completed workflow state for inclusion in the results.

### Execution Mode Management
The benchmarking system provides granular control over execution modes (real vs. mock) for different components like LLMs, tools, and database interactions. This is primarily managed through the `WheelGenerationState` and a centralized agent configuration helper.

#### `WheelGenerationState` Model for Execution Control
The `WheelGenerationState` model, defined in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:146-152), is crucial for managing execution modes within workflows like wheel generation. It includes specific fields to control whether real or mock components are used:

```python
# From backend/apps/main/graphs/wheel_generation_graph.py
class WheelGenerationState(BaseModel):
    # ... other state fields ...
    use_real_llm: bool = Field(default=False, description="Whether to use real LLM services instead of mocks")
    use_real_tools: bool = Field(default=False, description="Whether to use real tool implementations instead of mocks")
    use_real_db: bool = Field(default=False, description="Whether to use real database operations instead of mocks")
    mock_tools: Optional[Any] = Field(default=None, description="Mock tool registry for partial mocking")
    # ... other state fields ...
```
**Key Properties:**
- **Default Behavior**: All execution modes default to `False` (i.e., mock mode).
- **Explicit Configuration**: The desired execution mode must be explicitly requested when initiating a benchmark or workflow.
- **Granular Control**: Each component (LLM, tools, DB) can be independently set to real or mock.
- **Mock Tool Registry**: Supports partial mocking scenarios by allowing a `mock_tools` registry to be passed in the state.

State Initialization for workflows, as seen in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:291-300), includes these parameters:
```python
# Example from backend/apps/main/graphs/wheel_generation_graph.py
initial_state = WheelGenerationState(
    # ... other initial state parameters ...
    use_real_llm=use_real_llm,        # Execution mode control
    use_real_tools=use_real_tools,    # Execution mode control
    use_real_db=use_real_db,          # Execution mode control
    mock_tools=mock_tools             # Mock tool registry
)
```

#### Agent Configuration for Execution Modes
A centralized helper function, `_configure_agent_for_execution_mode(state: WheelGenerationState, agent_name: str, user_profile_id: Optional[str])` located in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:169-175), is used within agent nodes to configure individual agents based on the execution mode parameters present in the `WheelGenerationState`.

```python
# Pattern from backend/apps/main/graphs/wheel_generation_graph.py
async def agent_node(state: WheelGenerationState) -> Dict[str, Any]:
    logger.debug(f"🎭 {agent_name} executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")
    agent_kwargs = _configure_agent_for_execution_mode(state, agent_name, user_profile_id)
    agent = AgentClass(**agent_kwargs)
    result = await agent.process(state)
    # ...
    return result
```
This helper function is responsible for:
- **LLM Configuration**: If `state.use_real_llm` is true, it attempts to select and configure an appropriate real LLM service (e.g., preferring gpt-4o-mini, with fallbacks). Otherwise, it sets up for mock LLM usage.
- **Database Service**: Configures the agent to use the real database service if `state.use_real_db` is true.
- **Tool Registry**: If `state.use_real_tools` is true, it prepares the agent to use real tool implementations. If false and `state.mock_tools` is provided, it uses the mock tool registry.
- **Error Handling**: Includes mechanisms for graceful fallbacks and detailed logging if a requested real component cannot be configured.

#### Real Mode Implementation Details
All workflow agents have been updated to support real mode execution through the `_configure_agent_for_execution_mode` helper.
- **Supported Agents**: This includes the Orchestrator, Resource, Engagement Analytics, Psychological Monitoring, Strategy, Wheel/Activity, Ethical Oversight, and Error Handler agents.
- **Execution Mode Matrix**: The system supports a flexible matrix of execution modes:
  | Component          | Mock Mode                         | Real Mode                             | Hybrid Mode                         |
  |--------------------|-----------------------------------|---------------------------------------|-------------------------------------|
  | **LLM Calls**      | Simulated responses               | Real API calls to configured models   | Configurable per agent              |
  | **Tool Execution** | Mock tool registry                | Real tool implementations             | Mixed real/mock tools               |
  | **Database Ops**   | Mock database service             | Real database with transactions       | Real DB with mock tools             |
- **Configuration Examples**:
  ```python
  # Full real mode
  state = WheelGenerationState(use_real_llm=True, use_real_tools=True, use_real_db=True)

  # Hybrid mode - real LLM, mock tools, real DB
  state = WheelGenerationState(use_real_llm=True, use_real_tools=False, use_real_db=True, mock_tools=MockToolRegistry())

  # Mock mode (default)
  state = WheelGenerationState() # All flags default to False
  ```

### Benchmark Execution Flow

#### Dual Execution Paths (Real vs. Mock)
The `WheelWorkflowBenchmarkManager` (defined in [`backend/apps/main/services/wheel_workflow_benchmark_manager.py`](backend/apps/main/services/wheel_workflow_benchmark_manager.py:)) employs a dual execution architecture. Based on the execution mode parameters (`use_real_llm`, `use_real_tools`, `use_real_db`), it decides whether to run the actual workflow or a mock version:

```python
# Conceptual logic in WheelWorkflowBenchmarkManager
if use_real_llm or use_real_tools or use_real_db:
    # Run real workflow using the actual run_wheel_generation_workflow
    output = await self._run_real_wheel_generation_workflow(scenario, params, workflow_input_data)
else:
    # Run mock workflow
    output = await self._run_mock_wheel_generation_workflow(scenario, params)
```
- **Real Workflow Path**: This path invokes the actual `run_wheel_generation_workflow` function (from [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:)). The `WheelGenerationState` within this workflow is initialized with the requested execution mode parameters. If real components are not fully implemented or face configuration issues, this path might raise errors (e.g., `NotImplementedError` historically, or specific configuration errors now).
- **Mock Workflow Path**: This path uses a dedicated `_run_mock_wheel_generation_workflow` method. This method simulates the workflow's execution and generates mock agent outputs, avoiding engagement with real external services or complex internal logic.

#### Workflow Function Interface
The primary workflow function, `run_wheel_generation_workflow` (located in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:305-311)), is designed to accept execution mode parameters. When called by the benchmarking system, these are typically passed via a `workflow_input` dictionary:

```python
# From backend/apps/main/graphs/wheel_generation_graph.py
async def run_wheel_generation_workflow(
    user_profile_id: Optional[str] = None,
    context_packet: Optional[Dict[str, Any]] = None,
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None # For benchmarking interface
) -> Dict[str, Any]:
    # Extracts use_real_llm, use_real_tools, use_real_db, mock_tools from workflow_input
    # Initializes WheelGenerationState with these parameters
    # ... rest of the workflow logic ...
```
This interface allows the `WheelWorkflowBenchmarkManager` to precisely control the execution mode of the workflow being benchmarked.

---

## Detailed Workflow Specifications

### Wheel Generation Workflow

**Purpose**: Create personalized activity recommendations based on user context and profile.

**Agent Sequence**:
1.  **Orchestrator** → Initial workflow setup and context distribution
2.  **Resource Agent** → Environmental and resource analysis
3.  **Engagement Analytics** → Historical pattern analysis
4.  **Psychological Monitoring** → Current state and trust assessment
5.  **Strategy Agent** → Comprehensive strategy synthesis
6.  **Wheel/Activity Agent** → Activity selection and wheel construction
7.  **Ethical Oversight** → Final validation and approval
8.  **Orchestrator** → Final integration and delivery preparation

**Key Outputs**:
*   Personalized activity wheel with 6-8 activities
*   Probability-weighted activity distribution
*   Detailed activity instructions and requirements
*   Value propositions connecting activities to user goals

**Success Criteria**:
*   All activities pass ethical validation
*   Challenge levels appropriate for trust phase
*   Resource requirements match user availability
*   Domain distribution aligns with preferences and growth needs

### Evaluation Criteria and Optimization Guidelines

The evaluation criteria have been clarified to reflect the architectural reality where the Mentor is the primary user-facing agent, while other agents function as specialized tools within workflows.

#### Agent Evaluation Criteria

##### Mentor Agent Evaluation Criteria

**Communication Quality Assessment**:
*   **Tone Appropriateness**: Emotional tone suitable for user's trust level and context
*   **Empathy and Support**: Emotional understanding and supportive communication
*   **Trust Building**: Effectiveness in building and maintaining user trust
*   **Clarity**: Clear, understandable communication appropriate for user's level
*   **Responsiveness**: Appropriate response to user's emotional state and needs

**Decision Making Quality**:
*   **Workflow Routing**: Accuracy in selecting appropriate workflows for user needs
*   **Context Assessment**: Quality of user context analysis and interpretation
*   **Boundary Respect**: Appropriate respect for user autonomy and boundaries
*   **Escalation Decisions**: When and how to escalate or seek additional support

**LLM Configuration Optimization**:
*   **Model Selection**: Compare performance across different LLM models for user interaction
*   **Temperature Tuning**: Balance between empathy/creativity and consistency
*   **Token Efficiency**: Minimize token usage while maintaining communication quality
*   **Cost Optimization**: Balance performance with operational costs

##### Specialized Agent Evaluation Criteria

**Structured Output Quality**:
*   **Schema Compliance**: Adherence to defined output schemas and data contracts
*   **Data Completeness**: Thoroughness of analysis within agent's domain
*   **Accuracy**: Correctness of domain-specific insights and recommendations
*   **Integration Quality**: How well outputs integrate with downstream agents

**Domain Expertise Assessment**:
*   **Resource Agent**: Accuracy of resource and capacity assessments
*   **Engagement Agent**: Quality of user pattern and preference analysis
*   **Psychological Agent**: Appropriateness of psychological state assessments
*   **Strategy Agent**: Effectiveness of strategy synthesis and recommendations
*   **Activity Agent**: Relevance and quality of activity selections
*   **Ethical Agent**: Thoroughness of safety and ethical validations

#### Workflow Evaluation Criteria

**Output Quality Focus** (Primary Evaluation Area):
*   **Relevance**: How well final outputs (e.g., wheel items) match user needs and context
*   **Appropriateness**: Suitability of content for user's trust level, preferences, and situation
*   **Completeness**: Thoroughness of the final deliverable
*   **Personalization**: Degree of customization based on user profile and context
*   **Actionability**: Practical value and implementability of recommendations

**System Performance**:
*   **Coordination Efficiency**: Clean data transfer and handoffs between agents
*   **State Consistency**: Proper maintenance of workflow state across agent transitions
*   **Error Handling**: How well errors are contained, handled, and recovered from
*   **Resource Utilization**: Efficiency in token usage, execution time, and system resources
*   **End-to-End Latency**: Total workflow execution time
*   **Stage Bottlenecks**: Identification of performance constraints
*   **Scalability**: Performance under different load conditions

**Workflow Integrity**:
*   **Execution Flow**: Proper sequence and conditional logic
*   **Data Validation**: Input/output validation at each stage
*   **Error Handling**: Graceful failure management
*   **User Experience**: Smooth and responsive interaction flow

**Note**: Tone analysis and communication style evaluation are **not** part of workflow evaluation criteria. These aspects are evaluated exclusively in Mentor agent benchmarks, as workflows focus on output quality and system performance rather than communication style.

### Optimization Strategies

#### For Agent Performance

1.  **Iterative Prompt Engineering**:
    *   Test different instruction formulations
    *   Measure semantic quality across dimensions
    *   Optimize for specific use cases and contexts

2.  **LLM Configuration Tuning**:
    *   Systematic testing of model/temperature combinations
    *   Cost-benefit analysis for different configurations
    *   Context-specific optimization (trust levels, user types)

3.  **Context Sensitivity Analysis**:
    *   Identify performance variations across user contexts
    *   Develop context-specific adaptations
    *   Ensure consistent quality across all scenarios

#### For Workflow Performance

1.  **Bottleneck Identification**:
    *   Analyze stage-by-stage performance metrics
    *   Identify coordination inefficiencies
    *   Optimize critical path components

2.  **State Management Optimization**:
    *   Minimize state transfer overhead
    *   Ensure data consistency across agents
    *   Implement efficient error recovery

3.  **Agent Coordination Enhancement**:
    *   Improve handoff protocols
    *   Reduce redundant processing
    *   Optimize parallel execution opportunities

### Discussion Workflow

**Purpose**: Handle general conversation, clarification requests, and information collection.

**Agent Sequence**:
1.  **Orchestrator** → Goal determination and context setup
2.  **Mentor Agent** → Conversational interaction (looping until goal met)
3.  **Orchestrator** → Completion assessment and termination

**Conversation Loop Logic**:
```python
while not state.is_goal_met:
    mentor_response = mentor_agent.process(state)
    state.update(mentor_response)
    if conversation_limit_reached or goal_achieved:
        state.is_goal_met = True
```

**Use Cases**:
*   General chat and relationship building
*   Missing information collection for other workflows
*   Clarification of user intent or system capabilities
*   Error communication and recovery guidance

### Onboarding Workflow

**Purpose**: Transform completed questionnaire responses into comprehensive user profile.

**Agent Sequence**:
1.  **Orchestrator** → Questionnaire analysis and data organization
2.  **Resource Agent** → Environmental context and resource establishment
3.  **Engagement Analytics** → Preference and pattern analysis
4.  **Psychological Monitoring** → Comprehensive psychological assessment
5.  **Strategy Agent** → Initial strategy framework creation
6.  **Ethical Oversight** → Profile validation and safety review
7.  **Orchestrator** → Final profile integration and completion

**Profile Components Created**:
*   Demographics and basic user information
*   HEXACO personality trait assessments
*   Environmental context and resource inventory
*   Skills, capabilities, and limitations
*   Beliefs, goals, and inspirational sources
*   Trust baseline and communication preferences
*   Initial strategy framework for future interactions

---

## Advanced Technical Details

### Agent Communication Protocols

**Context Packet Standards**:
*   Standardized format across all agent interactions
*   Confidence scoring for all extracted information
*   Metadata tracking for audit and debugging
*   Session information for result routing

**State Management**:
*   Immutable state transitions with full audit trail
*   Rollback capabilities for error recovery
*   Concurrent access protection for multi-agent workflows
*   Memory persistence across workflow boundaries

### Memory Architecture

**Agent-Specific Memory**:
```python
# Psychological Monitoring Agent Memory
{
    "trait_expression_patterns": {
        "conscientiousness": {
            "baseline": 45,
            "growth_trajectory": "increasing",
            "confidence": 0.78,
            "last_updated": "2023-10-15T14:30:00Z"
        }
    },
    "trust_development_history": [...],
    "growth_opportunity_indicators": [...]
}
```

**Workflow Memory**:
```python
# Strategy Agent Memory
{
    "goal_trait_skill_mapping": {...},
    "challenge_calibration_rationale": {...},
    "domain_engagement_strategy": {...},
    "growth_facilitation_approach": {...},
    "trust_development_pathway": {...}
}
```

### Performance Optimization

**Caching Strategies**:
*   Agent memory caching with TTL policies
*   Frequently accessed user data caching
*   GenericActivity catalog caching
*   Workflow result caching for similar contexts

**Parallel Processing**:
*   Independent agent processing where possible
*   Async/await patterns for I/O operations
*   Database connection pooling
*   Redis-based result caching

**Monitoring and Metrics**:
*   Agent execution time tracking
*   Workflow completion rates
*   Error frequency by agent and workflow type
*   User satisfaction correlation with agent performance

---

## Development Guidelines

### Adding New Agents

1.  **Define Agent Responsibility**: Clear, focused purpose
2.  **Specify Data Access**: Read/write permissions and memory requirements
3.  **Design Processing Logic**: Input validation, core processing, output formatting
4.  **Implement Base Class**: Inherit from LangGraphAgent
5.  **Add to Workflow**: Define position in agent sequence
6.  **Create Tests**: Unit tests and integration tests
7.  **Document Outputs**: Expected output format and quality benchmarks

### Creating New Workflows

1.  **Define Workflow Purpose**: Clear objective and success criteria
2.  **Design Agent Sequence**: Optimal order and dependencies
3.  **Specify State Model**: Required state fields and transitions
4.  **Implement Routing Logic**: Conditional edges and error handling
5.  **Add Result Handling**: Output formatting and delivery
6.  **Create Documentation**: Flow diagrams and technical specifications
7.  **Implement Testing**: End-to-end workflow testing

### Quality Assurance Standards

**Agent Output Requirements**:
*   Confidence scores for all assessments (0-100)
*   Supporting evidence for all recommendations
*   Clear rationales for all decisions
*   Appropriate error handling and fallbacks

**Workflow Validation**:
*   Complete state transitions without data loss
*   Proper error propagation and recovery
*   Consistent output formatting
*   Performance within acceptable time limits

**Ethical Compliance**:
*   All outputs reviewed by Ethical Oversight Agent
*   User well-being prioritized over system efficiency
*   Transparent decision-making processes
*   Respect for user autonomy and boundaries

---

## Troubleshooting Guide

### Common Issues

**Agent Timeout Errors**:
*   Check database connection health
*   Verify memory access patterns
*   Review processing complexity
*   Implement fallback responses

**Workflow State Corruption**:
*   Validate state model consistency
*   Check for concurrent access issues
*   Review error handling logic
*   Implement state recovery mechanisms

**Memory Inconsistencies**:
*   Verify memory update patterns
*   Check for race conditions
*   Review confidence score calculations
*   Implement memory validation

**Performance Degradation**:
*   Monitor agent execution times
*   Check database query efficiency
*   Review caching effectiveness
*   Analyze workflow bottlenecks

### Debugging Tools

**Workflow Tracing**:
*   Complete execution logs with timestamps
*   State transition tracking
*   Agent input/output logging
*   Error context preservation

**Performance Monitoring**:
*   Agent execution time metrics
*   Memory usage tracking
*   Database query analysis
*   Cache hit/miss ratios

**Quality Validation**:
*   Output format validation
*   Confidence score verification
*   Ethical compliance checking
*   User satisfaction correlation

---

## Data Model Enhancement Analysis (June 2025)

### Current System Assessment

The wheel generation workflow and benchmarking system provide a solid foundation for debugging but have opportunities for enhancement to achieve the high-quality debugging experience we're targeting.

#### Current Strengths
- **Comprehensive Workflow State Tracking**: Complete capture of agent outputs and state transitions
- **Enhanced Agent Communications**: Rich metadata with mentor-relevant insights and execution context
- **Execution Mode Tracking**: Detailed tracking of real vs mock execution per agent
- **Token and Cost Tracking**: Enhanced tracking with agent-specific estimates and real usage data
- **Tool Usage Analysis**: Comprehensive tool call tracking with mock vs real differentiation

#### Identified Enhancement Opportunities

##### 1. Agent-Level Debugging Granularity
**Current Gap**: Limited visibility into agent internal processing
**Enhancement Needed**:
- Agent input data capture alongside output data
- Intermediate reasoning steps and decision points
- LLM interaction details (prompts, responses, model parameters)
- Tool call sequences with full execution context

##### 2. LLM Interaction Transparency
**Current Gap**: No capture of actual prompts or raw LLM responses
**Enhancement Needed**:
- Full prompt engineering visibility for optimization
- Raw LLM response analysis for quality assessment
- Model-specific performance pattern tracking
- Detailed cost attribution per agent and decision point

##### 3. Enhanced Error Context
**Current Gap**: Limited context about error occurrence and resolution
**Enhancement Needed**:
- Detailed error context with execution state
- Error resolution path tracking
- Fallback usage effectiveness analysis
- Predictive error detection capabilities

### Implementation Roadmap

#### Phase 1: Core Debugging Infrastructure (High Priority)
1. **Enhanced Agent Communication Tracking**
   - Capture agent input data alongside output data
   - Track processing context and decision points
   - Implement standardized agent execution logging

2. **LLM Interaction Logging**
   - Store actual prompts sent to LLMs
   - Capture raw responses and model parameters
   - Track token usage and performance per interaction

3. **Error Context Enhancement**
   - Detailed error context capture
   - Resolution path tracking
   - Fallback usage monitoring

#### Phase 2: Advanced Monitoring (Medium Priority)
1. **Real-Time State Tracking**
   - Intermediate state capture during execution
   - State consistency validation across agents
   - Performance bottleneck identification

2. **Tool Call Sequence Analysis**
   - Detailed tool call context and sequencing
   - Tool performance and reliability metrics
   - Usage pattern optimization

#### Phase 3: Predictive Analytics (Low Priority)
1. **Alternative Path Analysis**
   - Decision alternatives and confidence tracking
   - Path optimization opportunity identification
   - Quality prediction modeling

2. **Proactive Quality Management**
   - Real-time quality prediction during execution
   - Early warning systems for quality issues
   - Automated optimization recommendations

### Expected Impact

#### For Debugging Experience
- **Complete Visibility**: Full transparency into workflow execution from input to output
- **Root Cause Analysis**: Precise identification of failure points with full context
- **Performance Optimization**: Data-driven insights for workflow and agent optimization
- **Quality Assurance**: Comprehensive quality tracking with predictive capabilities

#### For Development Workflow
- **Faster Debugging**: Reduced time to identify and fix issues with detailed execution traces
- **Better Testing**: More comprehensive test coverage with execution context validation
- **Improved Reliability**: Enhanced error handling with fallback effectiveness tracking
- **Enhanced Monitoring**: Real-time visibility into system health and performance patterns

### Technical Implementation Notes

The enhanced data model should integrate seamlessly with existing infrastructure:
- **BenchmarkRun.agent_communications**: Expand to include enhanced agent execution data
- **BenchmarkRun.raw_results**: Include LLM interaction logs and detailed error context
- **AgentCommunicationTracker**: Enhance to capture input data and processing steps
- **Admin Interface**: Update modals to display enhanced debugging information

This analysis provides the foundation for implementing comprehensive debugging capabilities that will significantly improve development efficiency and system reliability.
