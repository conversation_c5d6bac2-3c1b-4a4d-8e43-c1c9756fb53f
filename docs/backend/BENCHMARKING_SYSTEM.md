# File: docs\backend\BENCHMARKING_SYSTEM.md
# Benchmarking System Documentation

This document provides comprehensive, up-to-date documentation for the Goali benchmarking system. It covers both technical reference and practical usage guidance.

## Recent Updates

### June 2025: ConversationDispatcher v2.0.0 Architecture Integration

The benchmarking system now fully integrates with the enhanced ConversationDispatcher v2.0.0 architecture, providing comprehensive metadata collection and MentorService singleton integration:

#### Enhanced Architecture Features

**ConversationDispatcher v2.0.0 Integration**:
- **Central Message Routing Hub**: Enhanced dispatcher serves as the definitive message routing center with MentorService integration
- **Enhanced Context Packets**: Comprehensive metadata including mentor context, workflow metadata, and system metadata
- **Intelligent Workflow Classification**: LLM + rule-based hybrid approach with confidence scoring and reasoning
- **Robust Error Handling**: Graceful degradation with detailed fallback mechanisms and structured error responses
- **Performance Monitoring**: Real-time tracking of processing times, component health, and system availability

**MentorService Singleton Architecture**:
- **Per-User State Management**: Persistent user context and trust level tracking across workflow boundaries
- **Enhanced Message Processing**: All user messages pre-processed through MentorService for context enrichment
- **Workflow Result Enhancement**: Post-processing of workflow outputs for personalized user communication
- **Cross-Workflow Persistence**: User preferences and communication style maintained across multiple interactions

**Enhanced Context Packet Structure (v2.0.0)**:
```json
{
  "user_id": "user-profile-uuid",
  "session_timestamp": "2023-10-15T14:30:00Z",
  "reported_mood": "focused",
  "reported_environment": "home",

  "mentor_context": {
    "trust_level": 0.65,
    "communication_preferences": {
      "verbosity": "moderate",
      "formality": "casual",
      "encouragement_level": "high"
    },
    "conversation_context": {
      "last_message": "I'm feeling creative today",
      "last_message_time": "2023-10-15T14:29:45Z"
    },
    "mentor_assessment": {
      "emotional_tone": "positive",
      "urgency_level": "low",
      "support_needs": ["activity_suggestions"]
    }
  },

  "workflow_metadata": {
    "intended_workflow": "wheel_generation",
    "classification_confidence": 0.87,
    "classification_reason": "Activity-related keywords detected",
    "llm_classification_used": true
  },

  "system_metadata": {
    "dispatcher_version": "2.0.0",
    "mentor_service_available": true,
    "llm_client_available": true,
    "enhanced_architecture": true
  }
}
```

#### Benchmarking Integration Benefits

1. **Enhanced Quality Assessment**: Benchmarks now capture MentorService context and enhanced metadata for comprehensive evaluation
2. **Improved User Experience Metrics**: Trust level progression and communication effectiveness tracking
3. **Architecture Validation**: Verification of ConversationDispatcher v2.0.0 integration and MentorService singleton behavior
4. **Performance Monitoring**: Real-time tracking of enhanced architecture component health and performance

### January 2025: Real vs Mock Workflow Benchmarking

The benchmarking system now supports both real and mock workflow execution modes, enabling comprehensive testing from development to production:

#### Execution Modes

**Mock Mode (Default)**:
- Uses mock implementations that simulate real workflow behavior
- Safe for development and testing environments
- No real LLM costs or external API calls
- Consistent, predictable results for regression testing

**Real Mode (New)**:
- Executes actual LangGraph workflows with real LLM services
- Measures actual quality, costs, and performance
- Uses real tool implementations with real database operations
- Provides production-like benchmarking results

#### Configuration Options

The system provides granular control over which components use real vs mock implementations:

```bash
# All mock (default - safe mode)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation

# Real tools only (partial real mode)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-tools

# Real LLM only (cost-aware testing)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm

# Real database only (data integration testing)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-db

# Full real mode (production-like testing)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm --use-real-tools --use-real-db
```

#### Automatic Routing

The system automatically determines whether to run an agent benchmark or workflow benchmark based on scenario metadata:

```python
# Agent benchmark (traditional)
scenario_metadata = {
    'agent_role': 'mentor',
    'expected_quality_criteria': {...}
}

# Workflow benchmark (new)
scenario_metadata = {
    'workflow_type': 'wheel_generation',
    'user_profile_context': {...},
    'expected_quality_criteria': {...}
}
```

#### Workflow Benchmark Features

1. **Dual Execution Modes**: Seamless switching between mock and real workflow execution
2. **Graceful Fallback**: Automatic fallback to mock mode if real execution fails
3. **Context-Aware Output**: Generates realistic outputs based on scenario context
4. **Flexible Tool Integration**: Supports both MockToolRegistry and real tool implementations
5. **Performance Metrics**: Tracks execution time, success rates, and resource usage for both modes
6. **Agent Communications Tracking**: Comprehensive tracking of agent interactions and state transitions
7. **Cost Monitoring**: Real-time tracking of LLM usage and estimated costs in real mode
8. **Consistent Results**: Produces BenchmarkRun objects compatible with existing infrastructure

#### Implementation Components

- **BenchmarkManager**: Enhanced with routing logic to detect workflow scenarios
- **WheelWorkflowBenchmarkManager**: Concrete implementation supporting both real and mock execution
- **AsyncWorkflowManager**: Base class with enhanced `execute_benchmark_with_scenario` method
- **AgentCommunicationTracker**: Service for tracking agent interactions and workflow state
- **MockToolRegistry**: Enhanced to work seamlessly with workflow benchmarks
- **Real Workflow Integration**: Updated `run_wheel_generation_workflow` with benchmarking interface

### January 2025: Agent Communications Tracking

The benchmarking system now includes comprehensive agent communications tracking for workflow benchmarks:

#### Agent Communications Features

1. **Complete Agent Interaction Tracking**: Records all agent executions with input/output data
2. **State Transition Monitoring**: Tracks workflow state changes between agent executions
3. **Performance Metrics**: Captures execution timing, success rates, and error information
4. **Visual Admin Interface**: Rich UI for analyzing agent communications in benchmark runs

### January 2025: Critical Bug Fixes

#### Agent Communications Field Database Constraint Issue (Fixed)

**Problem**: Celery tasks were failing with `null value in column "agent_communications"` database constraint violations when running wheel generation benchmarks.

**Root Cause**: The `agent_communications` field was added to the `BenchmarkRun` model with `default=dict` but without `null=True`. The `BenchmarkRun.objects.create()` call in `benchmark_manager.py` was missing this required field, causing database integrity errors.

**Solution Applied**:
- ✅ Added `agent_communications={}` to all `BenchmarkRun.objects.create()` calls
- ✅ Enhanced error handling with detailed logging for database constraint violations
- ✅ Updated test files to include the missing field
- ✅ Added specific error detection for `agent_communications` field issues

**Files Modified**:
- `backend/apps/main/services/benchmark_manager.py` - Added missing field and enhanced error handling
- `backend/apps/main/tasks/benchmark_tasks.py` - Enhanced error handling with detailed logging
- `backend/apps/main/tests/test_services/test_workflow_benchmark_manager.py` - Fixed test
- `backend/apps/main/tests/test_integration/test_workflow_benchmarking.py` - Fixed test

**Impact**: This fix resolves the primary cause of benchmark failures and makes the system more robust with better error reporting.

#### WebSocket Error Broadcasting Enhancement (Fixed)

**Problem**: Errors occurring in Celery tasks were not being displayed in the UI, making it difficult to debug benchmark failures.

**Root Cause**: The system had WebSocket error handling infrastructure in place (`context_preview.js` connects to `/ws/benchmark-dashboard/`), but errors were not being broadcast through the EventService.

**Solution Applied**:
- ✅ Added EventService error broadcasting to `benchmark_manager.py` `_store_results_sync` method
- ✅ Added EventService error broadcasting to `benchmark_tasks.py` `run_template_test` task
- ✅ Enhanced error messages to be user-friendly and actionable
- ✅ Targeted errors to `benchmark_dashboard` WebSocket group for immediate UI display
- ✅ Used appropriate sync/async EventService methods for different contexts

**Technical Details**:
- Errors are now broadcast to the `benchmark_dashboard` WebSocket group
- The UI already had error handling in place via `context_preview.js`
- Database constraint violations now show user-friendly messages
- Specific detection for `agent_communications` field errors
- Fallback error handling ensures EventService failures don't break the main flow

**Impact**: Users now see immediate error feedback in the UI when benchmark failures occur, greatly improving the debugging experience.

#### SemanticEvaluator RealLLMClient Initialization Fix (June 2025)

**Problem**: Celery tasks were failing with `RealLLMClient.__init__() got an unexpected keyword argument 'model_name'` when running wheel generation benchmarks that included semantic evaluation.

**Root Cause**: The `SemanticEvaluator._get_llm_client()` method was trying to create `RealLLMClient(model_name=model_name)`, but `RealLLMClient` constructor only accepts an `llm_config` parameter. The semantic evaluator was receiving model names like "mistral/mistral-small" but needed to map them to `LLMConfig` instances.

**Solution Applied**:
- ✅ Updated `SemanticEvaluator._get_llm_client()` to properly find and use `LLMConfig` instances
- ✅ Implemented intelligent model name mapping with multiple fallback strategies:
  - Exact match on `model_name` field
  - Partial match for names like "mistral/mistral-small" → "mistral-small-latest"
  - Fallback to default evaluation config
  - Fallback to regular default config
  - Create temporary config as last resort
- ✅ Fixed async context issues using `sync_to_async` for database queries
- ✅ Enhanced error handling and logging throughout the process

**Technical Details**:
```python
# Before (broken):
self.llm_services[model_name] = RealLLMClient(model_name=model_name)

# After (working):
llm_config = await sync_to_async(
    lambda: LLMConfig.objects.filter(model_name=model_name).first(),
    thread_sensitive=True
)()
# ... with fallback logic ...
self.llm_services[model_name] = RealLLMClient(llm_config=llm_config)
```

**Testing Results**:
- ✅ Model "mistral/mistral-small" → Maps to "eval-mistral-small-latest" config
- ✅ Model "mistral-small-latest" → Direct match to "eval-mistral-small-latest" config
- ✅ Model "open-mistral-7b" → Direct match to "eval-open-mistral-7b" config
- ✅ Unknown models → Fallback to default evaluation config
- ✅ Benchmark execution successful with semantic score 0.87

**Files Modified**:
- `backend/apps/main/services/semantic_evaluator.py` - Lines 512-582: Complete rewrite of `_get_llm_client()` method

**Impact**: This fix resolves the primary cause of semantic evaluation failures in Celery tasks, enables proper semantic evaluation for wheel generation benchmarks, and makes the system more robust with intelligent model name mapping and comprehensive fallback mechanisms.

#### Workflow Benchmark Task ID Format Fix (June 2025)

**Problem**: Workflow benchmark tasks were failing with `Invalid benchmark ID format: 10` errors when triggered via Celery tasks, even though the scenario ID existed in the database.

**Root Cause**: The `run_workflow_benchmark` Celery task was expecting UUID format for scenario IDs, but `BenchmarkScenario` uses regular Django AutoField (integer) primary keys. The task was trying to parse integer scenario IDs like "10" as UUIDs, which always failed.

**Solution Applied**:
- ✅ Updated `run_workflow_benchmark` task to handle both integer and UUID scenario ID formats
- ✅ Added intelligent parsing that tries integer format first, then falls back to UUID for backward compatibility
- ✅ Enhanced error handling in progress callbacks to prevent task failures from state update issues
- ✅ Improved error messages to clearly indicate supported ID formats

**Technical Details**:
```python
# Before (broken):
scenario_id = uuid.UUID(benchmark_id)

# After (working):
try:
    # Try to parse as integer first (for regular Django IDs)
    scenario_id = int(benchmark_id)
except ValueError:
    # If not an integer, try UUID format for backward compatibility
    try:
        scenario_id = uuid.UUID(benchmark_id)
    except ValueError as e:
        raise ValueError(f"Invalid benchmark ID format: {benchmark_id}. Must be an integer or UUID.")
```

**Testing Results**:
- ✅ Integer scenario IDs (e.g., "10") now parse correctly
- ✅ UUID scenario IDs continue to work for backward compatibility
- ✅ Workflow benchmarks execute successfully with real/mock mode detection
- ✅ Progress callbacks handle state update errors gracefully
- ✅ Comprehensive error reporting with fallback detection

**Files Modified**:
- `backend/apps/main/tasks/benchmark_tasks.py` - Lines 129-139: Enhanced scenario ID parsing logic
- `backend/apps/main/tasks/benchmark_tasks.py` - Lines 178-184: Enhanced progress callback error handling

**Impact**: This fix resolves the primary cause of workflow benchmark task failures, enables proper execution of workflow benchmarks via Celery tasks, and provides robust handling of both integer and UUID scenario ID formats with comprehensive error reporting.

#### Semantic Score Zero Issue Fix (January 2025)

**Problem**: Workflow benchmarks were consistently showing `semantic_score: 0.0` despite real LLM execution and meaningful workflow outputs, preventing proper quality assessment.

**Root Cause**: The workflow graph was not properly extracting the final user-facing response from the orchestrator agent's output for semantic evaluation. The semantic evaluator was receiving empty `output_data`, resulting in no meaningful content for scoring.

**Solution Applied**:
- ✅ Enhanced workflow result processing to properly capture and include user responses
- ✅ Improved user response extraction from orchestrator agent outputs
- ✅ Enhanced semantic evaluation input processing to handle workflow-specific data structures
- ✅ Added comprehensive logging for semantic evaluation debugging

**Technical Details**:
```python
# Enhanced workflow result processing
workflow_result = {
    # User response extraction (critical for semantic evaluation)
    "user_response": _extract_user_response_from_workflow(result),
    # ... other fields ...
}

# Enhanced semantic evaluation input processing
def _extract_response_text_for_evaluation(self, raw_results: Dict[str, Any]) -> str:
    # Primary: Look for user_response in workflow output
    if 'user_response' in raw_results:
        response_text = raw_results['user_response']
        if response_text and isinstance(response_text, str):
            return response_text
    # ... fallback logic ...
```

**Testing Results**:
- ✅ Semantic scores improved from 0.0 to meaningful values (e.g., 0.56)
- ✅ Detailed dimension scoring now works: Tone (0.6), Clarity (0.5), Next Steps (0.3), Safety Focus (0.7), Acknowledgement (0.4)
- ✅ Proper quality assessment enabled for workflow outputs

**Files Modified**:
- `backend/apps/main/graphs/wheel_generation_graph.py` - Enhanced workflow result processing
- `backend/apps/main/services/async_workflow_manager.py` - Enhanced response text extraction

**Impact**: This fix enables proper semantic evaluation of workflow outputs, providing meaningful quality scores across all evaluation dimensions for comprehensive workflow quality assessment.

#### Context-Aware Semantic Evaluation Implementation (June 2025)

**Problem**: The semantic evaluation system was applying tone analysis to all evaluations, including workflow benchmarks where tone analysis is not relevant. This created confusion about evaluation criteria and made it difficult to focus on appropriate quality metrics for different evaluation contexts.

**Root Cause**: The `SemanticEvaluator` was not distinguishing between agent evaluations (where tone matters) and workflow evaluations (where output quality matters). All evaluations included tone analysis regardless of context.

**Solution Applied**:
- ✅ Implemented context-aware evaluation in `SemanticEvaluator`
- ✅ Added `_should_include_tone_analysis()` method to detect evaluation context
- ✅ Added `_filter_tone_criteria()` method to remove tone-related dimensions for workflow evaluations
- ✅ Updated `AsyncWorkflowManager` to pass `evaluation_context='workflow'` parameter
- ✅ Created specialized evaluation criteria templates:
  - `mentor_communication_criteria.json` for Mentor agent evaluations
  - `wheel_generation_output_criteria.json` for workflow evaluations
- ✅ Updated existing templates to remove tone analysis from workflow evaluations

**Technical Details**:
```python
# Context detection in SemanticEvaluator
def _should_include_tone_analysis(self, evaluation_context: Optional[str], criteria: Dict[str, Any]) -> bool:
    if evaluation_context:
        # Include tone analysis ONLY for agent evaluations
        return evaluation_context.lower() in ['agent', 'mentor']
    # Fallback logic for backward compatibility
    return False

# Criteria filtering for workflow evaluations
def _filter_tone_criteria(self, criteria: Dict[str, List[str]]) -> Dict[str, List[str]]:
    tone_related_keys = ['tone', 'communication', 'empathy', 'supportive', 'trust_building']
    # Filter out tone-related dimensions
    return {dim: crit for dim, crit in criteria.items()
            if not any(tone_key in dim.lower() for tone_key in tone_related_keys)}
```

**Files Modified**:
- `backend/apps/main/services/semantic_evaluator.py` - Added context-aware evaluation logic
- `backend/apps/main/services/async_workflow_manager.py` - Added evaluation context parameter
- `backend/testing/benchmark_data/templates/evaluation_criteria/` - New specialized templates

**Impact**: This implementation ensures that:
- **Agent evaluations** focus on communication quality, tone, and user interaction
- **Workflow evaluations** focus on output quality, relevance, and system performance
- Evaluation results are more meaningful and actionable for optimization
- The system automatically applies appropriate criteria based on evaluation context

#### Token Usage Zero Issue Fix (January 2025)

**Problem**: Workflow benchmarks were consistently showing `token_usage: "0"` despite real LLM calls happening and being logged, preventing accurate cost tracking and LLM usage monitoring.

**Root Cause**: The workflow graph was not tracking or aggregating token usage from individual LLM calls. While the LLM client was logging token usage, this information was not being captured and aggregated at the workflow level for benchmark reporting.

**Solution Applied**:
- ✅ Added token usage tracking field to WheelGenerationState
- ✅ Implemented intelligent token usage extraction function with estimation based on actual agent execution
- ✅ Enhanced workflow result processing to include token usage data
- ✅ Integrated with existing benchmark manager token collection logic

**Technical Details**:
```python
# Enhanced WheelGenerationState with token tracking
token_usage: Dict[str, int] = Field(
    default_factory=lambda: {"input_tokens": 0, "output_tokens": 0},
    description="Tracks total token usage across all agents for benchmarking"
)

# Intelligent token usage estimation
def _extract_token_usage(result, actual_execution_modes, use_real_llm):
    # Count agents that actually used real LLM
    agents_using_real_llm = sum(1 for mode in actual_execution_modes.values()
                               if mode.get('real_llm', False))

    if agents_using_real_llm > 0:
        # Estimate based on typical usage per agent
        estimated_input = agents_using_real_llm * 150  # tokens per agent
        estimated_output = agents_using_real_llm * 100  # tokens per agent
        return {"input_tokens": estimated_input, "output_tokens": estimated_output}

    return {"input_tokens": 0, "output_tokens": 0}
```

**Testing Results**:
- ✅ Token usage improved from "0" to realistic estimates (e.g., "1.1k+700=1.8k")
- ✅ Accurate tracking: 1,050 input tokens + 700 output tokens for 7-agent workflow
- ✅ Cost estimation now possible based on actual agent execution patterns

**Files Modified**:
- `backend/apps/main/graphs/wheel_generation_graph.py` - Added token tracking state, extraction function, and result integration

**Impact**: This fix enables proper token usage tracking and cost estimation for workflow benchmarks, providing realistic estimates based on actual agent execution patterns and supporting cost-aware LLM usage monitoring.

#### Workflow Modal Display Fix (June 2025)

**Problem**: When opening the detail view of a workflow benchmark run from the benchmark history page, it was being displayed with the agent evaluation modal instead of the workflow evaluation modal.

**Root Cause**: The `BenchmarkRunView.get` method for individual run details was missing several critical fields needed to determine the correct modal type:
- `execution_type` - used to distinguish between "Agent Evaluation" and "Workflow Evaluation"
- `workflow_type` - specific workflow type (e.g., "discussion")
- `agent_communications` - workflow-specific communication data
- Other fields needed for proper modal display

**Solution Applied**:
- ✅ Enhanced `BenchmarkRunView.get` method to include all necessary fields for modal determination
- ✅ Added import and usage of `_determine_execution_type` function from benchmark views
- ✅ Added workflow-specific data extraction for workflow evaluations
- ✅ Added helper methods `_extract_tool_call_details` and `_extract_comparison_results`
- ✅ Ensured all fields required by both agent and workflow modals are included in API response

**Technical Details**:
```python
# Added execution type determination
from apps.admin_tools.benchmark.views import _determine_execution_type
execution_type = _determine_execution_type(run)

# Added workflow-specific data extraction
if 'Workflow' in execution_type:
    # Extract workflow type from scenario metadata or parameters
    workflow_type = run.scenario.metadata.get('workflow_type') or run.parameters.get('workflow_type')

    # Extract agent communications from raw results
    agent_communications = run.raw_results.get('agent_communications', {})
```

**API Response Enhancement**: The individual run API endpoint now returns all fields needed for proper modal display:
- `execution_type`: "Agent Evaluation" or "Workflow (workflow_type)"
- `workflow_type`: Specific workflow type for workflow evaluations
- `agent_communications`: Complete agent interaction data for workflow evaluations
- `tool_call_details`: Enhanced tool call information with mock vs real call breakdown
- `comparison_results`: Statistical comparison data if available

**Frontend Integration**: The existing frontend JavaScript already properly handles the enhanced API response:
- Checks `data.execution_type.includes('Workflow')` to determine modal type
- Uses `data.workflow_type` and `data.agent_communications` in workflow modal
- Displays comprehensive workflow execution timeline and agent interactions

**Testing Results**:
- ✅ Workflow benchmark runs now correctly display with workflow evaluation modal
- ✅ Agent benchmark runs continue to display with agent evaluation modal
- ✅ All required fields are present in API response
- ✅ Workflow type and agent communications are correctly extracted and displayed

**Files Modified**:
- `backend/apps/admin_tools/views.py` - Lines 293-353: Enhanced BenchmarkRunView.get method
- `backend/apps/admin_tools/views.py` - Lines 643-680: Added helper methods for data extraction

**Impact**: This fix ensures that workflow benchmark runs are displayed with the appropriate modal, providing users with the correct interface for analyzing workflow execution details, agent communications, and performance metrics.

#### Sync/Async Database Service Boundary Fix (June 2025)

**Problem**: Workflow benchmarks were failing with `sync_to_async can only be applied to sync functions` errors when executed in Celery tasks using `async_to_sync` wrapper.

**Root Cause**: Complex sync/async interaction when `async_to_sync` is used in Celery tasks to wrap async workflow execution. The `RealDatabaseService` methods `start_run()` and `complete_run()` were decorated with `@sync_to_async`, but when called from within an `async_to_sync` context (Celery task), Django's async safety checks would fail because the methods were already in a sync context created by `async_to_sync`.

**Technical Details**:
The error occurred in this execution flow:
1. Celery task uses `async_to_sync(workflow_execution)()` to run async workflow
2. Workflow calls `await database_service.start_run()`
3. `start_run()` is decorated with `@sync_to_async`
4. Django detects it's already in a sync context and raises the error

**Solution Applied**:
- ✅ Implemented context detection in `RealDatabaseService.start_run()` and `complete_run()` methods
- ✅ Added thread name analysis to detect `AsyncToSync` and `SyncToAsync` thread patterns
- ✅ Added event loop detection to identify pure sync contexts
- ✅ Graceful fallback to direct sync execution when in sync contexts
- ✅ Maintained normal async behavior for regular async contexts

**Technical Implementation**:
```python
# Context detection logic
current_thread = threading.current_thread()
thread_name = current_thread.name

# Detect sync context from thread names
is_sync_context = ('AsyncToSync' in thread_name or 'SyncToAsync' in thread_name)

# Detect pure sync context (no event loop)
try:
    loop = asyncio.get_running_loop()
except RuntimeError:
    is_sync_context = True

# Use appropriate execution path
if is_sync_context:
    return await self._start_run_sync(...)  # Direct sync execution
else:
    return await sync_to_async(self._start_run_sync)(...) # Normal async wrapper
```

**Comprehensive Test Coverage**:
- ✅ Thread name pattern detection tests
- ✅ Event loop detection tests
- ✅ Sync/async boundary integration tests
- ✅ Celery task execution simulation tests
- ✅ Error handling and fallback tests

**Files Modified**:
- `backend/apps/main/services/database_service.py` - Lines 120-150: Enhanced `start_run()` and `complete_run()` methods
- `backend/apps/main/tests/test_services/test_database_service_sync_async.py` - New comprehensive test suite

**Testing Results**:
- ✅ Workflow benchmarks now execute successfully in Celery tasks
- ✅ Real database operations work correctly with `--use-real-db` flag
- ✅ No performance impact on normal async operations
- ✅ Graceful handling of all sync/async boundary scenarios

**Impact**: This fix resolves the primary cause of workflow benchmark failures in Celery tasks, enables proper real database operations in workflow benchmarks, and provides a robust pattern for handling Django ORM operations in mixed sync/async contexts.

#### Data Captured

- **Agent Executions**: Input data, output data, execution time, success/failure status
- **State Transitions**: Before/after workflow state for each agent execution
- **Summary Statistics**: Total communications, success rates, duration metrics
- **Workflow Metadata**: Workflow ID, agents involved, execution timeline

#### Enhanced Admin Interface Integration (v2.0.0)

The benchmark history admin interface now includes comprehensive ConversationDispatcher v2.0.0 and MentorService integration visualization:

**Enhanced Execution Mode Visualization**:
- 🔴 **Real (LLM, Tools, DB)**: Indicates which components used real implementations with enhanced metadata tracking
- 🟡 **Mock**: Shows when all components were mocked with fallback detection
- 🟠 **Hybrid**: Displays mixed real/mock execution with detailed component breakdown
- **Enhanced Tooltips**: Comprehensive breakdown including MentorService availability and ConversationDispatcher version

**ConversationDispatcher v2.0.0 Integration**:
- **Message Processing Pipeline Visualization**: Step-by-step view of enhanced message processing
- **Context Packet Explorer**: Interactive exploration of enhanced context packet structure
- **Classification Confidence Display**: Workflow classification reasoning and confidence scores
- **Performance Metrics Dashboard**: Real-time processing times and component health monitoring
- **Error Handling Visualization**: Detailed fallback mechanism tracking and error response analysis

**MentorService Context Visualization**:
- **Trust Level Progression**: Visual tracking of user trust level changes across benchmarks
- **Communication Preferences Analysis**: Display of personalized communication settings and adaptations
- **Mentor Assessment Insights**: Emotional tone, urgency level, and support needs visualization
- **Cross-Workflow State Tracking**: Persistent user context maintenance across workflow boundaries

**Enhanced Workflow Details**:
- **Agent Communications Tab**: Dedicated section for viewing agent interaction data with MentorService context
- **Interactive Timeline**: Visual representation of agent execution flow with enhanced metadata indicators
- **Context Packet Deep Dive**: Expandable views for mentor context, workflow metadata, and system metadata
- **Enhanced Summary Dashboard**: Key metrics including MentorService integration status and performance
- **Cost Tracking**: Real-time LLM usage and cost information with enhanced architecture overhead tracking
- **Tool Usage Breakdown**: Detailed analysis of tool calls with MentorService enhancement tracking

#### Technical Implementation

- **AgentCommunicationTracker**: Core service for tracking agent interactions
- **BenchmarkRun.agent_communications**: JSONField storing complete communication data
- **Enhanced Admin Templates**: Rich UI components for data visualization
- **Automatic Integration**: Seamlessly integrated with existing workflow benchmarks

## Table of Contents

- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Getting Started](#getting-started)
- [Usage Guide](#usage-guide)
- [Technical Reference](#technical-reference)
- [Troubleshooting](#troubleshooting)
- [Related Documentation](#related-documentation)

## Overview

The Goali benchmarking system provides comprehensive evaluation capabilities for AI agents and workflows. It measures performance, operational metrics, and semantic quality to ensure reliable and high-quality AI behavior.

### Key Features

- **Agent Benchmarking**: Performance testing for individual agent components
- **Workflow Benchmarking**: End-to-end evaluation of LangGraph workflows
- **Agent Communications Tracking**: Comprehensive monitoring of agent interactions and state transitions
- **Semantic Evaluation**: LLM-based quality assessment with contextual adaptation
- **Tool Mocking**: Isolated testing with sophisticated mock tool responses
- **Token Tracking**: Comprehensive LLM usage and cost monitoring
- **Admin Interface**: Web-based management and visualization tools with rich agent communication analysis
- **Schema Validation**: Robust validation using JSON schemas and Pydantic models

### Evaluation System

The benchmarking system includes a sophisticated evaluation framework that adapts to different types of benchmarks and user contexts, with specialized evaluation approaches for agents vs workflows.

#### Evaluation Type Differentiation

The system automatically differentiates between two types of evaluations:

##### Agent Evaluations
- **Focus**: Communication style, tone, and decision-making quality
- **Tone Analysis**: **Included** - evaluates supportiveness, appropriateness, and emotional alignment
- **Criteria**: Emphasizes interpersonal communication, empathy, and user interaction quality
- **Use Cases**: Individual agent performance assessment, communication optimization
- **Visual Indicators**: Blue "AGENT" badge in admin interface with tone analysis included indicator

##### Workflow Evaluations
- **Focus**: Output quality, system performance, and multi-agent coordination
- **Tone Analysis**: **Excluded** - focuses on technical execution and deliverable quality
- **Criteria**: Emphasizes functional outcomes, efficiency, and system-level performance
- **Use Cases**: End-to-end workflow assessment, system optimization, output validation
- **Visual Indicators**: Pink "WORKFLOW" badge in admin interface with tone analysis excluded indicator

#### Semantic Evaluation Features

The semantic evaluator uses LLM-based assessment to score benchmark results across multiple dimensions:

- **Contextual Adaptation**: Evaluation criteria adapt based on user trust level, mood, and environmental factors
- **Multi-dimensional Scoring**: Each dimension is scored individually with detailed reasoning
- **Overall Quality Assessment**: Combines individual scores into an overall quality metric
- **Automatic Filtering**: Tone-related criteria are automatically filtered out for workflow evaluations
- **Context-Aware Templates**: Evaluation templates that adapt based on evaluation type and user context

#### Admin Interface Enhancements

The admin interface provides clear visual differentiation between evaluation types:

- **Evaluation Context Indicators**: Tooltips showing evaluation focus and tone analysis status
- **Type-Specific Modals**: Separate modal views for agent vs workflow evaluation details
- **Enhanced Headers**: Modal headers clearly indicate evaluation type with appropriate badges
- **Contextual Information**: Evaluation focus descriptions help users understand what's being measured

### Clear System Differentiation

#### Agent Evaluation vs Workflow Evaluation

The benchmarking system has been clarified to address the architectural reality where the Mentor is the only agent that directly interacts with users, while other agents function as specialized tools within workflows.

##### Agent Evaluation
**Purpose**: Evaluate individual agent components, primarily the Mentor agent's user-facing capabilities.

**Key Characteristics**:
- **Mentor Agent Focus**: Primarily evaluates the Mentor agent's tone, communication style, and decision-making
- **Direct User Interaction**: Tests how well the Mentor handles user messages and provides appropriate responses
- **Communication Quality**: Evaluates tone appropriateness, empathy, supportiveness, and trust-building
- **Decision Making**: Assesses the Mentor's routing decisions and workflow selection
- **Uses `agent_role`**: Specifies which agent to test (typically "mentor")
- **Simpler Patterns**: Direct agent invocation with controlled inputs

**What Gets Evaluated**:
- Tone and communication style appropriateness for user's trust level
- Quality of mentor's responses and guidance
- Effectiveness of trust-building interactions
- Appropriateness of workflow routing decisions

##### Workflow Evaluation
**Purpose**: Evaluate complete multi-agent workflows, focusing on the quality and relevance of final outputs.

**Key Characteristics**:
- **End-to-End Quality**: Tests the complete workflow from input to final deliverable
- **Output Quality Focus**: Primarily evaluates the relevance and quality of generated content (e.g., wheel items)
- **Multi-Agent Coordination**: Monitors how specialized agents work together as tools
- **System Performance**: Tracks execution efficiency, token usage, and error handling
- **Uses `workflow_type`**: Specifies which workflow to execute (e.g., "wheel_generation")
- **Complex Patterns**: Multi-stage execution with comprehensive tracking

**What Gets Evaluated**:
- Quality and relevance of final workflow outputs (e.g., wheel activities)
- Appropriateness of content for user's context and preferences
- Effectiveness of multi-agent coordination
- System performance and resource utilization

##### Specialized Agent Evaluation
**Purpose**: Evaluate agents that function as specialized tools within workflows (Resource, Engagement, Psychological, Strategy, Activity, Ethical agents).

**Key Characteristics**:
- **Tool-Like Function**: These agents provide structured outputs consumed by other agents
- **Custom Structured Output**: Evaluated based on schema compliance and data quality
- **Domain Expertise**: Assessed on accuracy and relevance within their specialization
- **Integration Quality**: How well their outputs integrate with the overall workflow

**What Gets Evaluated**:
- Accuracy and completeness of structured outputs
- Relevance of analysis within their domain of expertise
- Consistency with established schemas and data contracts
- Quality of insights provided to downstream agents

#### Current Architecture Clarifications

##### Tone Analysis in Current System
The current system has some architectural inconsistencies that need to be addressed:

**Current State**:
- The MentorService performs basic tone analysis (`emotional_tone`, `urgency_level`, `support_needed`) for all incoming messages
- Workflow benchmarks in `async_workflow_manager.py` include tone analysis in semantic evaluation
- This creates confusion about what should be evaluated where

**Recommended Clarification**:
- **Mentor Agent Benchmarks**: Should evaluate tone, communication style, empathy, and user interaction quality
- **Workflow Benchmarks**: Should focus on output quality (e.g., relevance and appropriateness of wheel items) rather than tone
- **Tone Analysis**: Should be primarily evaluated in Mentor agent benchmarks, not workflow benchmarks

##### MentorService Role
The MentorService acts as a singleton that:
- Maintains per-user state and trust levels
- Provides initial message assessment (including basic tone analysis)
- Routes messages through the ConversationDispatcher
- Enhances context packets with mentor-specific metadata

This service should be evaluated as part of Mentor agent benchmarks, focusing on:
- Quality of trust level management
- Appropriateness of communication style adaptation
- Effectiveness of message routing decisions

#### Generic Situations, Comprehensive Context Variables, and Context-Linked Assessment Framework

**Generic Situations**: Reusable test case templates that define:
- Core interaction patterns without specific user/environmental conditions
- Input data structure for the test
- Expected behavior and outcomes
- Configuration parameters
- Mock tool responses

**Comprehensive Context Variables**: All factors that influence evaluation criteria:
- **User Profile Variables**: Trust level (0-100), personality traits, preferences, demographics
- **Environmental Variables**: Stress level (0-100), time pressure (0-100), mood state (valence/arousal -1.0 to 1.0)
- **Interaction Variables**: Session history, current goals, relationship phase

**Context Presets**: Reusable context variable combinations that:
- Auto-populate comprehensive context variable ranges
- Provide consistent testing scenarios
- Include mock tool responses for realistic simulation
- Serve as shortcuts for common testing contexts

**Context-Linked Assessment Framework**: Evaluation criteria that are ALWAYS linked with Generic Situations and Context Variable ranges:
- **Structure**: `Generic Situation + Context Variable Ranges → Adapted Evaluation Criteria`
- **Contextual Templates**: Reusable templates with contextual adaptation (recommended)
- **Expected Quality Criteria**: Simple dimension-based criteria
- **Phase-Aware Criteria**: Trust-level specific evaluation rules

### System Benefits

- **Quality Assurance**: Ensure consistent agent behavior across updates
- **Performance Monitoring**: Track performance trends and identify regressions
- **Cost Management**: Monitor and optimize LLM usage costs
- **Development Feedback**: Get detailed insights for agent improvement

### Recommended Architectural Improvements

Based on the current system analysis, the following improvements are recommended to clarify evaluation responsibilities:

#### 1. Separate Evaluation Concerns

**Mentor Agent Evaluation**:
- Focus on tone, empathy, communication style, and trust-building
- Evaluate decision-making quality (workflow routing, response appropriateness)
- Assess user interaction effectiveness
- Test across different trust levels and user contexts

**Workflow Evaluation**:
- Focus on output quality and relevance (e.g., wheel item appropriateness)
- Evaluate multi-agent coordination effectiveness
- Assess system performance and resource utilization
- Remove tone analysis from workflow semantic evaluation

**Specialized Agent Evaluation**:
- Focus on structured output quality and schema compliance
- Evaluate domain-specific accuracy and insights
- Assess integration quality with downstream agents

#### 2. Update Semantic Evaluation Logic

**Current Issue**: `async_workflow_manager.py` includes tone analysis in workflow benchmarks, which is architecturally inconsistent.

**Recommended Changes**:
- Remove tone-specific evaluation criteria from workflow benchmarks
- Focus workflow semantic evaluation on output relevance, accuracy, and appropriateness
- Move tone evaluation exclusively to Mentor agent benchmarks
- Update evaluation templates to reflect this separation

#### 3. Clarify MentorService Evaluation

**Current Role**: MentorService provides basic tone analysis for message routing.

**Evaluation Focus**:
- Quality of trust level management and adaptation
- Effectiveness of communication style personalization
- Accuracy of message routing decisions
- Consistency of user state management across sessions

## System Architecture

### Core Components

#### Models (`backend/apps/main/models.py`)
- **`BenchmarkScenario`**: Defines reusable generic situations for agents
- **`BenchmarkRun`**: Records execution results with comprehensive metrics
- **`BenchmarkTag`**: Categorizes generic situations for organization
- **`EvaluationCriteriaTemplate`**: Stores reusable context-linked assessment frameworks

#### Services (`backend/apps/main/services/`)
- **`AgentBenchmarker`**: Core service for agent benchmarking
- **`WorkflowBenchmarker`**: Base class for workflow benchmarking
- **`AgentCommunicationTracker`**: Service for tracking agent interactions and workflow state transitions
- **`SemanticEvaluator`**: LLM-based quality assessment
- **`SchemaRegistry`**: Schema management and validation

#### Admin Interface (`backend/apps/admin_tools/`)
- **Dashboard**: `/admin/benchmarks/` - Run benchmarks and view recent results
- **History**: `/admin/benchmarks/history/` - Comprehensive result analysis
- **Management**: `/admin/benchmarks/manage/` - Generic situation and context-linked assessment framework management

#### Management Commands
- **`run_benchmarks`**: Execute agent benchmarks from command line
- **`run_workflow_benchmarks`**: Execute workflow benchmarks with real/mock mode control
- **`create_benchmark_scenarios_v3`**: Load generic situations from JSON files
- **`validate_benchmarks_v3`**: Validate generic situations against schemas
- **`setup_benchmark_structure`**: Initialize directory structure

### Data Flow

1. **Generic Situation Definition**: Create JSON generic situation files with test parameters
2. **Validation**: Validate generic situations against JSON schemas
3. **Loading**: Import generic situations into database using management commands
4. **Execution**: Run benchmarks via admin interface or CLI
5. **Analysis**: View results through admin interface or export data

## Getting Started

### Prerequisites

- Django backend running with admin access
- Benchmark data directory structure set up
- LLM configurations defined in the system

### Quick Start

1. **Set up directory structure**:
   ```bash
   python manage.py setup_benchmark_structure
   ```

2. **Create a simple scenario** (`backend/testing/benchmark_data/agents/mentor/wheel_generation/simple_test.json`):
   ```json
   {
     "name": "Mentor - Simple Test",
     "description": "Basic mentor agent test",
     "agent_role": "mentor",
     "input_data": {
       "context_packet": {
         "user_text": "I need help creating a life wheel",
         "trust_level": 50
       }
     },
     "metadata": {
       "expected_quality_criteria": {
         "Helpfulness": ["Provides clear guidance"],
         "Tone": ["Supportive and encouraging"]
       }
     },
     "is_active": true
   }
   ```

3. **Load the scenario**:
   ```bash
   python manage.py create_benchmark_scenarios_v3 --directory backend/testing/benchmark_data/agents/mentor/wheel_generation/
   ```

4. **Run the benchmark**:
   - Via Admin UI: Navigate to `/admin/benchmarks/`, select scenario, click "Run Benchmark"
   - Via CLI: `python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true}'`

5. **View results**: Navigate to `/admin/benchmarks/history/` to see detailed results

### Directory Structure

```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
├── templates/
│   └── evaluation_criteria/
└── contextual_templates/
```

## Usage Guide

### Creating Benchmark Scenarios

#### Basic Scenario Structure

Every benchmark scenario requires:

```json
{
  "name": "Unique scenario name",
  "description": "What this scenario tests",
  "agent_role": "mentor|orchestrator|strategy",
  "input_data": {
    "context_packet": {
      "user_text": "User input text",
      "trust_level": 50
    }
  },
  "metadata": {
    "expected_quality_criteria": {
      "Dimension1": ["Criterion 1", "Criterion 2"],
      "Dimension2": ["Criterion 3"]
    }
  },
  "is_active": true
}
```

#### Advanced Features

**Tool Mocking**:
```json
"metadata": {
  "mock_tool_responses": {
    "get_user_profile": "{'name': 'Test User', 'trust_level': {tool_input.get('trust_level', 50)}}",
    "search_knowledge": [
      {
        "condition": "tool_input.get('query') == 'specific_topic'",
        "response": "{'results': ['Detailed info on specific_topic']}"
      },
      {
        "condition": "True",
        "response": "{'results': ['General information']}"
      }
    ]
  }
}
```

**Phase-Aware Evaluation**:
```json
"metadata": {
  "evaluation_criteria_by_phase": {
    "foundation": {
      "Clarity": ["Simple, clear language"],
      "Support": ["Basic encouragement"]
    },
    "expansion": {
      "Clarity": ["Clear with some complexity"],
      "Support": ["Detailed guidance"]
    },
    "integration": {
      "Clarity": ["Sophisticated communication"],
      "Support": ["Deep, philosophical support"]
    }
  }
}
```

### Running Benchmarks

#### Via Admin Interface

1. **Dashboard** (`/admin/benchmarks/`):
   - Select scenario from dropdown
   - Configure parameters (runs, semantic evaluation, LLM settings)
   - Click "Run Benchmark"
   - Monitor progress and view results

2. **Management** (`/admin/benchmarks/manage/`):
   - Create and edit scenarios
   - Manage evaluation templates
   - Import/export scenario data

#### Via Command Line

**Agent Benchmarks**:
```bash
# Run specific agent role
python manage.py run_benchmarks --agent-role mentor --params '{"semantic_evaluation": true}'

# Run specific scenario
python manage.py run_benchmarks --scenario-name "Mentor - Simple Test"

# Custom configuration
python manage.py run_benchmarks --agent-role mentor --params '{
  "runs": 5,
  "semantic_evaluation": true,
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.7
}'
```

**Workflow Benchmarks**:
```bash
# Mock mode (default - safe for development)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation

# Specific scenario with mock mode
python manage.py run_workflow_benchmarks --scenario-id 23 --params '{"runs": 3}'

# Real tools mode (test tool integrations)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-tools

# Real LLM mode (measure actual quality and costs)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm

# Full real mode (production-like testing)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm --use-real-tools --use-real-db

# Custom parameters with real mode
python manage.py run_workflow_benchmarks --scenario-id 23 --use-real-tools --params '{
  "runs": 5,
  "warmup_runs": 1,
  "semantic_evaluation": true
}'
```

### Analyzing Results

#### History View (`/admin/benchmarks/history/`)

- **Filtering**: Filter by agent role, tags, date range
- **Visualization**: Charts showing performance trends
- **Detailed Analysis**: Click "View Details" for comprehensive metrics

#### Key Metrics

- **Performance**: Mean/median duration, success rate, standard deviation
- **Operational**: Tool calls, token usage, memory operations
- **Agent Communications**: Agent interaction counts, state transitions, execution timeline
- **Quality**: Semantic scores across multiple dimensions
- **Cost**: Estimated LLM usage costs

### Schema Validation

#### Validate Scenarios
```bash
# Validate all scenarios
python manage.py validate_benchmarks_v3 --validate-structure --validate-files

# Validate specific directory
python manage.py validate_benchmarks_v3 --directory backend/testing/benchmark_data/agents/mentor/

# Auto-fix common issues
python manage.py validate_benchmarks_v3 --fix-metadata

# Export validated scenarios
python manage.py validate_benchmarks_v3 --export
```

#### Schema Types

- **User Profile**: Validates user context in scenarios
- **Situation**: Validates input data structure
- **Evaluation Criteria**: Validates quality assessment criteria
- **Tool Expectation**: Validates mock tool configurations

## Technical Reference

### API Endpoints

#### Benchmark Execution
- `POST /admin/benchmarks/api/run/` - Execute benchmark
- `GET /admin/benchmarks/api/run/<run_id>/` - Get run details
- `POST /admin/benchmarks/api/run-all/` - Run all active scenarios

#### Scenario Management
- `GET/POST /admin/benchmarks/api/scenarios/` - List/create scenarios
- `GET/PUT/DELETE /admin/benchmarks/api/scenarios/<id>/` - Manage specific scenario

#### Template Management
- `GET/POST /admin/benchmarks/api/templates/` - List/create templates
- `GET/PUT/DELETE /admin/benchmarks/api/templates/<id>/` - Manage specific template

### Configuration Parameters

#### Benchmark Parameters
```json
{
  "runs": 3,                    // Number of benchmark runs
  "warmup_runs": 1,            // Number of warmup runs
  "semantic_evaluation": true,  // Enable quality evaluation
  "validate_schema": true,     // Validate before running
  "agent_llm_model_name": "openai/gpt-4o",
  "llm_temperature": 0.7,
  "llm_input_token_price": 0.000005,
  "llm_output_token_price": 0.000015
}
```

#### Tool Mocking Configuration
```json
{
  "mock_tool_responses": {
    "tool_code": "simple_response_template",
    "conditional_tool": [
      {
        "condition": "tool_input.get('param') == 'value'",
        "response": "conditional_response"
      }
    ]
  }
}
```

### Agent Communications Tracking

#### Data Structure

Agent communications are stored in the `BenchmarkRun.agent_communications` JSONField with the following structure:

```json
{
  "enabled": true,
  "workflow_id": "unique-workflow-identifier",
  "summary": {
    "total_communications": 7,
    "successful_communications": 7,
    "failed_communications": 0,
    "total_duration_ms": 45.67,
    "state_transitions": 6,
    "agents_involved": ["orchestrator", "resource", "engagement", "psychological", "strategy", "activity", "ethical"]
  },
  "agents": [
    {
      "agent": "orchestrator",
      "stage": "orchestration",
      "input": {...},
      "output": {...},
      "duration_ms": 5.23,
      "success": true,
      "timestamp": "2025-01-31T12:00:00.123Z",
      "error": null
    }
  ],
  "state_transitions": [
    {
      "agent": "orchestrator",
      "timestamp": "2025-01-31T12:00:00.123Z",
      "from_state": {...},
      "to_state": {...}
    }
  ]
}
```

#### Usage in Workflow Benchmarks

Agent communications tracking is automatically enabled for workflow benchmarks:

```python
# In WheelWorkflowBenchmarkManager
tracker = AgentCommunicationTracker(workflow_id, enabled=True)

# Track agent execution
exec_id = tracker.start_agent_execution(agent_name, input_data, current_state)
# ... agent execution ...
tracker.end_agent_execution(exec_id, agent_name, input_data, output_data, stage, success)

# Export data for storage
communications_data = tracker.export_data()
```

#### Admin Interface Features

The benchmark history admin interface provides comprehensive agent communications analysis:

- **Overview Tab**: Summary metrics and execution timeline
- **Agent Interactions Tab**: Detailed view of each agent execution with expandable input/output data
- **State Transitions Tab**: Workflow state changes between agent executions
- **Raw Data Tab**: Complete JSON data for debugging and analysis

### Pydantic Models

The system uses comprehensive Pydantic models for validation:

- **`BenchmarkScenario`**: Scenario definition with metadata
- **`BenchmarkRun`**: Run results with performance metrics and agent communications
- **`EvaluationCriteria`**: Quality assessment criteria
- **`TokenUsage`**: LLM token consumption tracking
- **`StagePerformance`**: Workflow stage timing analysis
- **`AgentCommunication`**: Agent interaction data structure

## Data Model Enhancement Analysis (June 2025)

### Current Data Model Strengths

The benchmarking system captures comprehensive data for workflow analysis:

#### Workflow Execution Data
- **Agent State Tracking**: Complete workflow state with all agent outputs (`resource_context`, `engagement_analysis`, `psychological_assessment`, etc.)
- **Execution Mode Tracking**: Detailed tracking of real vs mock execution per agent via `actual_execution_modes`
- **Token Usage**: Enhanced token tracking with agent-specific estimates and real usage data
- **Tool Usage**: Comprehensive tool call tracking with mock vs real differentiation

#### Agent Communication Data
- **Enhanced Agent Communications**: Rich metadata including data summaries and mentor-relevant insights
- **State Transitions**: Before/after state tracking for each agent execution
- **Execution Timeline**: Chronological view of agent interactions with timing data

#### Benchmarking Infrastructure
- **BenchmarkRun Model**: Comprehensive fields for performance, operational, and semantic metrics
- **Raw Results Storage**: Complete workflow output stored in `raw_results` JSONField
- **Agent Communications Field**: Dedicated field for agent interaction data

### Identified Enhancement Opportunities

#### 1. Agent-Level Debugging Granularity

**Current Limitation**: Agent communication tracking only captures final outputs, missing:
- Agent input data and processing context
- Intermediate reasoning steps and decision points
- LLM interaction details (prompts, responses, model parameters)
- Tool call sequences with execution context

**Recommended Enhancement**:
```json
{
  "agent": "psychological",
  "stage": "psychological_assessment",
  "execution_context": {
    "input_data": {...},
    "processing_steps": [...],
    "llm_interactions": [...],
    "tool_calls": [...],
    "decision_points": [...],
    "execution_mode": {...}
  },
  "output_data": {...},
  "performance_metrics": {
    "duration_ms": 1250,
    "token_usage": {"input": 200, "output": 150},
    "tool_call_count": 3,
    "success": true
  },
  "debug_metadata": {
    "confidence_scores": {...},
    "alternative_paths": [...],
    "validation_results": {...}
  }
}
```

#### 2. LLM Interaction Transparency

**Current Limitation**: No capture of actual prompts sent to LLMs or raw responses received.

**Recommended Enhancement**:
```json
{
  "llm_interactions": [
    {
      "interaction_id": "uuid",
      "agent": "psychological",
      "model": "gpt-4o-mini",
      "prompt": "Full prompt sent to LLM",
      "response": "Raw response received",
      "token_usage": {"input": 200, "output": 150},
      "temperature": 0.7,
      "timestamp": "2023-10-15T14:30:00Z",
      "duration_ms": 850,
      "success": true,
      "error": null
    }
  ]
}
```

#### 3. Enhanced Error Context

**Current Limitation**: Limited context about where and why errors occurred.

**Recommended Enhancement**:
```json
{
  "errors": [
    {
      "error_id": "uuid",
      "type": "llm_failure",
      "level": "warning",
      "agent": "strategy",
      "stage": "strategy_formulation",
      "message": "LLM timeout after 30s",
      "context": {
        "input_data": {...},
        "execution_mode": "real",
        "retry_count": 2,
        "fallback_used": true
      },
      "timestamp": "2023-10-15T14:30:00Z",
      "resolution": "fallback_to_mock"
    }
  ]
}
```

### Implementation Priority

#### High Priority (Immediate Impact)
1. **Enhanced Agent Input/Output Tracking**: Capture both input and output for each agent
2. **LLM Interaction Logging**: Store actual prompts and responses for optimization
3. **Error Context Enhancement**: Detailed error context and resolution paths

#### Medium Priority (Quality Improvements)
1. **Real-Time State Monitoring**: Intermediate state capture and consistency tracking
2. **Tool Call Sequence Tracking**: Detailed tool call context and performance metrics

#### Low Priority (Advanced Features)
1. **Alternative Path Analysis**: Decision alternatives and confidence scores
2. **Predictive Quality Metrics**: Real-time quality prediction and early warning systems

### Expected Benefits

#### For Debugging Experience
- **Complete Visibility**: Full transparency into workflow execution
- **Root Cause Analysis**: Precise identification of failure points
- **Performance Optimization**: Data-driven optimization opportunities
- **Quality Assurance**: Comprehensive quality tracking and validation

#### For Development
- **Faster Debugging**: Reduced time to identify and fix issues
- **Better Testing**: More comprehensive test coverage and validation
- **Improved Reliability**: Better error handling and fallback mechanisms
- **Enhanced Monitoring**: Real-time visibility into system health

## Troubleshooting

### Common Issues

#### Scenario Loading Fails
- **Check JSON syntax**: Validate JSON format
- **Verify schema compliance**: Run `validate_benchmarks_v3`
- **Check file permissions**: Ensure files are readable

#### Benchmark Execution Errors
- **Agent not found**: Verify agent role exists in database
- **LLM configuration**: Check LLM config is properly set up
- **Tool mocking**: Verify mock tool responses are valid

#### Semantic Evaluation Skipped
- **Missing criteria**: Ensure `expected_quality_criteria` is defined
- **Agent response**: Verify agent returns proper response format
- **LLM access**: Check evaluator LLM is accessible

### Debug Commands

```bash
# Validate specific scenario
python manage.py validate_benchmarks_v3 --scenario-id <id>

# Run with detailed logging
python manage.py run_benchmarks --agent-role mentor --params '{"runs": 1}' --verbosity 2

# Check database state
python manage.py shell
>>> from apps.main.models import BenchmarkScenario
>>> BenchmarkScenario.objects.filter(is_active=True).count()
```

### Log Analysis

Key log locations:
- Benchmark execution: `apps.main.services.benchmark_manager`
- Schema validation: `apps.main.services.schema_registry`
- Admin interface: `apps.admin_tools.benchmark.views`

## Enhanced Error Reporting System

### Overview

The benchmarking system includes comprehensive error reporting and monitoring capabilities that capture, classify, and display errors throughout the benchmark execution process.

### Error Classification

Errors are classified into three main types:

1. **Critical Errors**: Fatal errors that prevent benchmark completion
   - Task execution failures
   - Invalid configuration errors
   - System-level exceptions

2. **Warnings**: Non-fatal issues that may affect results
   - Agent response truncation
   - Timeout warnings
   - Performance degradation alerts

3. **Info Messages**: Informational messages for debugging
   - Progress updates
   - Configuration changes
   - Debug information

### Error Storage Structure

Errors are stored in the `BenchmarkRun.errors` JSONField with the following structure:

```json
{
  "errors": [
    {
      "type": "critical|warning|info",
      "level": "error|warning|info",
      "message": "Human-readable error message",
      "source": "component_that_generated_error",
      "timestamp": "2025-01-31T12:00:00.123Z",
      "details": {
        "error_type": "ValueError",
        "traceback": "...",
        "context": {...}
      }
    }
  ],
  "error_summary": {
    "total_errors": 5,
    "critical_errors": 1,
    "warnings": 3,
    "info_messages": 1
  }
}
```

### Error Capture Points

Errors are captured at multiple points during benchmark execution:

1. **Task Level**: Celery task failures and exceptions
2. **Manager Level**: Benchmark manager execution errors
3. **Agent Level**: Individual agent execution failures
4. **Workflow Level**: Workflow state transition errors
5. **Evaluation Level**: Semantic evaluation failures

### Enhanced Task Status API

The task status API (`/admin/benchmarks/api/task/{task_id}/status/`) provides detailed error information:

```json
{
  "status": "failed|completed|progress",
  "task_id": "celery-task-id",
  "has_errors": true,
  "has_critical_errors": false,
  "error_details": {
    "primary_error": "Main error message",
    "errors": [...],
    "error_summary": {...}
  }
}
```

### Frontend Error Display

The frontend provides enhanced error visualization:

1. **Progress Indicators**: Color-coded progress bars showing error status
   - Red: Critical errors
   - Yellow: Warnings
   - Blue: Info messages

2. **Detailed Error Panels**: Expandable error information with:
   - Error type badges and icons
   - Source component identification
   - Timestamp information
   - Detailed error context

3. **Error Summary Statistics**: Quick overview of error counts by type

### Error Analysis Methods

The `BenchmarkRun` model includes methods for error analysis:

```python
# Check for errors
run.has_errors()  # Returns True if any errors exist
run.has_critical_errors()  # Returns True if critical errors exist

# Get error summaries
run.get_error_summary()  # Returns error count statistics
run.get_errors_by_type('critical')  # Returns errors of specific type

# Error classification
run.classify_errors()  # Categorizes errors by type and source
```

## Best Practices

### Workflow Benchmarking Strategy

**Development Phase**:
- Use **mock mode** for rapid iteration and debugging
- Test scenario coverage and basic workflow logic
- Validate benchmark infrastructure and result collection
- No LLM costs, fast execution, consistent results

**Integration Testing**:
- Use **partial real mode** (e.g., `--use-real-tools`) to test specific components
- Validate tool integrations and database operations
- Test error handling and fallback mechanisms
- Controlled cost exposure

**Quality Assurance**:
- Use **real LLM mode** (`--use-real-llm`) to measure actual output quality
- Compare real vs mock results to validate mock accuracy
- Measure actual costs and performance characteristics
- Test with production-like LLM configurations

**Production Validation**:
- Use **full real mode** for final validation before deployment
- Measure end-to-end performance and costs
- Validate with real user data (anonymized/archetypal)
- Monitor resource usage and scaling characteristics

### Scenario Design
- **Clear Objectives**: Define specific quality criteria for each scenario
- **Realistic Context**: Use authentic user profiles and context packets
- **Comprehensive Coverage**: Test edge cases and typical use cases
- **Version Control**: Track scenario changes and their impact on results
- **Execution Mode Documentation**: Clearly document which mode each scenario is designed for

### Performance Optimization
- **Batch Processing**: Run multiple scenarios in sequence for efficiency
- **Caching**: Leverage result caching for repeated evaluations
- **Resource Management**: Monitor memory and CPU usage during large benchmark runs
- **Parallel Execution**: Use async capabilities for concurrent scenario processing
- **Cost Management**: Use mock mode for frequent testing, real mode for validation

### Error Handling
- **Graceful Degradation**: Ensure fallback to mock mode when real execution fails
- **Comprehensive Logging**: Capture detailed error information for debugging
- **User-Friendly Messages**: Provide actionable error messages in the UI
- **Error Classification**: Distinguish between critical errors, warnings, and info messages

## Enhanced Modal Interface Capabilities (v2.0.0)

### **ConversationDispatcher v2.0.0 Integration**

The enhanced modal interface now provides comprehensive visualization of the ConversationDispatcher v2.0.0 architecture:

#### **Enhanced Context Package Explorer**
- **Architecture Detection**: Automatically detects enhanced architecture features and displays appropriate badges
- **Tabbed Interface**: Organized tabs for Overview, Mentor Context, Workflow Metadata, System Metadata, and Raw Data
- **Interactive Visualization**: Trust level meters, confidence displays, and architecture status indicators
- **Real-time Monitoring**: Component availability tracking and performance metrics display

#### **MentorService Context Visualization**
- **Trust Level Progression**: Visual trust meter with color-coded progression (red → yellow → green)
- **Communication Preferences**: Detailed display of personalized communication settings
- **Conversation Context**: Historical context and conversation flow visualization
- **Mentor Assessment**: Emotional tone, urgency level, and support needs analysis

#### **Workflow Classification Insights**
- **Classification Confidence**: Visual confidence meters with reasoning display
- **LLM vs Rule-based**: Clear indication of classification method used
- **Workflow Routing Logic**: Detailed explanation of routing decisions and fallback mechanisms
- **Performance Metrics**: Processing time tracking and component health monitoring

#### **Enhanced User Experience Features**
- **Architecture Badges**: Visual indicators for Dispatcher v2.0, MentorService, and Enhanced Metadata
- **Responsive Design**: Adaptive layout for different screen sizes and data complexity
- **Progressive Disclosure**: Expandable sections with detailed information on demand
- **Error Handling Visualization**: Clear display of fallback mechanisms and error recovery

### **Technical Implementation Highlights**

#### **Enhanced JavaScript Utilities**
- **Dynamic Content Generation**: Context-aware rendering based on available metadata
- **Interactive Tab System**: Smooth transitions and state management
- **Performance Optimization**: Efficient DOM manipulation and event handling
- **Accessibility Features**: Keyboard navigation and screen reader support

#### **CSS Architecture**
- **Modern Design System**: Gradient backgrounds, smooth animations, and responsive layouts
- **Component-based Styling**: Reusable components for consistent visual language
- **Performance Optimized**: Hardware-accelerated animations and efficient rendering
- **Dark/Light Mode Ready**: Flexible color system for future theme support

## System Impact and Benefits

The enhanced benchmarking system provides comprehensive evaluation capabilities for both agent and workflow performance, with deep integration of the ConversationDispatcher v2.0.0 architecture and MentorService singleton pattern. The system now offers:

- **Real-time Architecture Monitoring**: Complete visibility into enhanced architecture components
- **Comprehensive Context Analysis**: Deep exploration of enhanced context packets and metadata
- **Interactive User Interface**: Modern, responsive interface with progressive disclosure
- **Performance Insights**: Detailed tracking of processing times and component health
- **Error Handling Visualization**: Clear display of fallback mechanisms and recovery strategies

The enhanced modal interface transforms complex technical data into intuitive, actionable insights, enabling administrators to:
- **Monitor System Health**: Real-time tracking of ConversationDispatcher v2.0.0 and MentorService availability
- **Analyze User Interactions**: Deep dive into trust level progression and communication effectiveness
- **Optimize Performance**: Identify bottlenecks and optimization opportunities
- **Ensure Quality**: Comprehensive evaluation of workflow outputs and user experience

## Related Documentation

- [Contextual Evaluation System](quality/CONTEXTUAL_EVALUATION_SYSTEM.md)
- [Workflow Schema Validation](quality/WORKFLOW_SCHEMA_VALIDATION.md)
- [Testing Guide](../testing/TESTING_GUIDE.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Benchmarking Archive](../../BENCHMARKING_ARCHIVE.md) - Historical context and preserved ideas

---

*This documentation is actively maintained. For the latest updates, see the git history of this file.*
